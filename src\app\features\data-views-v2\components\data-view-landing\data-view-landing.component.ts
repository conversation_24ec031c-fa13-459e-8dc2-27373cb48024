import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  effect,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { SharedModule } from '../../../../shared/shared.module';
import { MatButtonModule } from '@angular/material/button';
import { DataViewSortComponent } from '../../shared/data-view-sort/data-view-sort.component';
import { MatIcon } from '@angular/material/icon';
import { DataViewTreeComponent } from '../../shared/data-view-tree/data-view-tree.component';
import { DataViewComponentStore } from '../../component-store/data-view.store';
import { CommonModule } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { DataViewFileUploadComponent } from '../data-view-file-upload/data-view-file-upload.component';
import { FileDirectoryEntry } from '../../shared/data-view.interfaces';
import { PreviewTableComponent } from '../../shared/preview-table/preview-table.component';

@Component({
  selector: 'app-data-view-landing',
  imports: [
    SharedModule,
    MatIcon,
    MatButtonModule,
    DataViewSortComponent,
    DataViewTreeComponent,
    CommonModule,
    PreviewTableComponent,
  ],
  templateUrl: './data-view-landing.component.html',
  standalone: true,
  providers: [],
  styleUrl: './data-view-landing.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataViewLandingComponent implements OnInit, OnDestroy {
  readonly dialog = inject(MatDialog);
  constructor(private cdr: ChangeDetectorRef) {}
  readonly store = inject(DataViewComponentStore);
  dataViewHierarchy: Record<number, FileDirectoryEntry[]> = {};
  title = '';
  isLoading = signal<boolean>(true);
  projectId = localStorage.getItem('project_id') || 0;
  isFileUploaded!: boolean;
  previewMetadata = {
    fileId: 0,
    showPreview: false,
  };

  ngOnInit(): void {
    this.store.setProjectDetails(Number(localStorage.getItem('project_id')));
    this.store.loadFiles(this.store.project_id(), true);
    this.store.setProjectInfo();
  }

  OnEffect = effect(() => {
    this.title = this.store.projectTitle();
    this.isLoading.set(this.store.isLoading());
    const isFileUploaded = this.store.isUploadDone();
    this.previewMetadata = this.store.previewFileMetadata();
    if (isFileUploaded) {
      this.store.resetState();
      this.store.loadFiles(this.store.project_id(), true);
      this.store.setIsUploadDone(false);
    }
    this.cdr.markForCheck();
  });

  uploadFileFolder() {
    this.dialog.open(DataViewFileUploadComponent, {
      ...DataViewFileUploadComponent.viewConfig,
    });
  }

  sortOptionChanged(event: { value: string; type: string }) {
    this.store.resetPartialState();
    this.store.setSortValue(
      event.value == 'initialSelect' ? '' : event.value,
      event.type,
    );
    this.store.loadFiles(this.store.project_id(), true);
  }

  ngOnDestroy(): void {
    this.store.resetState();
  }
}
