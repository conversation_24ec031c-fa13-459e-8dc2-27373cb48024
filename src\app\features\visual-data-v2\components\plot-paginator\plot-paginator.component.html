<div
  class="flex flex-row justify-end p-3 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-lg shadow-sm mx-2 border [border-color:#79747E1F]">
  <div class="flex items-center justify-center">
    {{ this.startItem() }} - {{ this.endItem() }} of
    {{ this.visualDataStore.pagination().totalItems }}
  </div>

  <div class="flex items-center justify-center">
    <button
      matIconButton
      (click)="this.gotoPreviousPage()"
      [disabled]="!this.isPrevisousPageAvailable()"
      class="flex items-center justify-center text-gray-600 dark:text-gray-300 disabled:text-gray-400 dark:disabled:text-gray-500">
      <mat-icon>navigate_before</mat-icon>
    </button>
  </div>

  <div class="flex items-center justify-center">
    <button
      matIconButton
      (click)="this.gotoNextPage()"
      [disabled]="!this.isNextPageAvailable()"
      class="flex items-center justify-center text-gray-600 dark:text-gray-300 disabled:text-gray-400 dark:disabled:text-gray-500">
      <mat-icon>navigate_next</mat-icon>
    </button>
  </div>
</div>
