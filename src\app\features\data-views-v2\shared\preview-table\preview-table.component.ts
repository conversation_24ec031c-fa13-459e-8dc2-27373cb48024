/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnInit,
} from '@angular/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { FileData } from '../../../data-views/models/data-view.model';
import { DataViewServiceV2 } from '../../services/data-view.service';
import { MatChipsModule } from '@angular/material/chips';
import { SharedModule } from '../../../../shared/shared.module';
import { CommonModule } from '@angular/common';
import { ReplaceUnderscorePipe } from '../../../data-views/pipes/replace-underscore.pipe';
import { CapitalizePipe } from '../../../data-views/pipes/capitalize.pipe';
import { DataViewComponentStore } from '../../component-store/data-view.store';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-preview-table',
  imports: [
    MatChipsModule,
    SharedModule,
    MatTableModule,
    CommonModule,
    ReplaceUnderscorePipe,
    CapitalizePipe,
  ],
  templateUrl: './preview-table.component.html',
  styleUrl: './preview-table.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  // standalone: false
})
export class PreviewTableComponent implements OnInit {
  readonly store = inject(DataViewComponentStore);
  // File Preview
  isImageFile = false;
  // UI State
  isTableVisible = false;
  isImageVisible = false;
  showAllChips = false;
  previewLoading = false;
  isComponentLoaded = false;
  // Data Management
  dataSource = new MatTableDataSource<FileData>([]); // FileData was any before
  displayedColumns: string[] = [];

  fileMetadata = {
    name: '',
    size: 0,
    resolution: '',
    totalColumns: 0,
    totalRows: 0,
    safeFileUrl: '',
  };

  constructor(
    private cdr: ChangeDetectorRef,
    private dataviewService: DataViewServiceV2,
    private toaster: ToastrService,
  ) {}
  ngOnInit(): void {
    this.PreviewFiles(this.store.previewFileMetadata().fileId);
  }

  PreviewFiles(fileID: number): void {
    this.setPreviewLoading(true);
    this.dataviewService.previewFiles(fileID).subscribe({
      next: (response: any) => {
        this.setPreviewLoading(false);
        if (response.status === 'success') {
          this.handlePreviewResponse(response);
          this.cdr.markForCheck();
        } else {
          this.toaster.error(response.message);
        }
      },
      error: () => {
        this.store.setPreviewFileId(fileID, false);
        this.setPreviewLoading(false);
        this.setVisibilityFlags({ table: false, image: false });
        this.cdr.markForCheck();
      },
    });
  }
  private setPreviewLoading(isLoading: boolean): void {
    this.previewLoading = isLoading;
  }

  private handlePreviewResponse(response: any): void {
    const metadata = response.data.metadata;

    if (response.data.json_data) {
      // Handle image preview
      this.fileMetadata = {
        ...this.fileMetadata,
        name: metadata.filename,
        size: metadata.file_size,
        resolution: metadata.file_size,
        safeFileUrl: response.data.json_data.layout.images[0].source,
      };
      this.setVisibilityFlags({ table: false, image: true });
    } else {
      // Handle table preview
      this.displayedColumns = response.data.columns;
      this.dataSource.data = response.data.data;
      this.fileMetadata = {
        ...this.fileMetadata,
        name: metadata.filename,
        size: metadata.file_size,
        totalColumns: metadata.num_columns,
        totalRows: metadata.num_rows,
      };
      this.setVisibilityFlags({ table: true, image: false });
    }
  }
  private setVisibilityFlags(flags: { table: boolean; image: boolean }): void {
    this.isTableVisible = flags.table;
    this.isImageVisible = flags.image;
  }
}
