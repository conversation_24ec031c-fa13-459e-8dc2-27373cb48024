import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnInit,
} from '@angular/core';
import { DataviewService } from '../../services/data-view.service';

@Component({
  selector: 'app-folder-panel',
  templateUrl: './folder-panel.component.html',
  styleUrls: ['./folder-panel.component.css'],
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class FolderPanelComponent implements OnInit, OnChanges {
  @Input() folder: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  @Input() rootFolderId?: number;
  @Input() showTable?: (fileId: number) => void;
  @Input() viewFile?: (fileId: number) => void;
  @Input() deleteFile?: (fileId: number) => void;
  @Input() deleteFolder?: (folderId: number) => void;
  @Input() PreviewFiles?: (fileId: number) => void;
  @Input() sortOption = '';

  count!: number;
  expandedPanels: Record<string, boolean> = {};
  folderIDToDelete: number | null = null;
  fileIDToDelete: number | null = null;
  isFolderDeletePopup = false;
  isFileDeletePopup = false;
  page_size = 10;
  showLoadMore = false;
  active = false;
  currentSelectedFolder = '0';

  constructor(
    private dataViewService: DataviewService,
    private cdr: ChangeDetectorRef,
  ) {}
  ngOnChanges(): void {
    const storedFolderId = localStorage.getItem('folder_id');
    this.currentSelectedFolder =
      this.currentSelectedFolder === '0'
        ? this.folder.id
        : this.currentSelectedFolder;
    this.active = storedFolderId == this.currentSelectedFolder ? true : false;
  }
  ngOnInit(): void {
    this.count = this.folder.count;
    if (this.count > this.page_size) this.showLoadMore = true;
  }

  getSubfolderData(subfolderId: string) {
    this.currentSelectedFolder = subfolderId;
    if (!subfolderId) return;
    const page = 1;
    const search_files = true;
    localStorage.setItem('folder_id', subfolderId);
    this.dataViewService
      .getAllDataView(
        subfolderId,
        page,
        this.page_size,
        search_files,
        this.sortOption,
      )
      .subscribe(
        response => {
          if (response.status === 'success') {
            const subfolderData = response.data;
            this.folder.files = subfolderData.files || [];
            this.folder.subfolders = subfolderData.subfolders || [];
            if (
              response.pagination &&
              response.pagination?.count > this.page_size
            ) {
              this.showLoadMore = true;
            } else {
              this.showLoadMore = false;
            }
            this.cdr.detectChanges();
          }
        },
        error => console.error('Error retrieving subfolder data:', error),
      );
  }

  loadMoreData(subFolderId: string) {
    this.page_size = this.page_size + 10;
    this.getSubfolderData(subFolderId);
  }
  togglePanel(folderId: number) {
    this.currentSelectedFolder = folderId.toString();
    this.expandedPanels[folderId] = !this.expandedPanels[folderId];
  }

  handleShowTable(fileId: number) {
    if (this.showTable) {
      this.showTable(fileId);
    } else {
      console.warn('showTable function is not defined');
    }
  }

  openFilePreview(fileID: number) {
    if (this.PreviewFiles) {
      this.PreviewFiles(fileID);
    }
  }

  handleViewFile(fileId: number) {
    if (this.viewFile) {
      this.viewFile(fileId);
    } else {
      console.warn('viewFile function is not defined');
    }
  }

  confirmDeleteFile(fileID: number) {
    this.fileIDToDelete = fileID;
    this.isFileDeletePopup = true;
  }

  confirmDeleteFolder(folderID: number) {
    this.folderIDToDelete = folderID;
    this.isFolderDeletePopup = true;
  }
  DeleteFileData(fileID: number) {
    if (this.deleteFile) {
      this.deleteFile(fileID);
      this.isFileDeletePopup = false;
      this.cdr.markForCheck();
    }
  }

  DeleteFolderData(folderID: number) {
    if (this.deleteFolder) {
      this.deleteFolder(folderID);
      this.isFolderDeletePopup = false;
    }
  }

  closePopup() {
    this.isFolderDeletePopup = false;
    this.isFileDeletePopup = false;
  }

  private removeFolderFromData(folderID: number) {
    this.folder.subfolders = this.folder.subfolders.filter(
      (subfolder: any) => subfolder.id !== folderID, // eslint-disable-line @typescript-eslint/no-explicit-any
    );
    this.cdr.markForCheck();
  }

  private removeFileFromData(fileID: number) {
    this.folder.files = this.folder.files.filter(
      (file: any) => file.id !== fileID, // eslint-disable-line @typescript-eslint/no-explicit-any
    );
    this.cdr.markForCheck();
  }
}
