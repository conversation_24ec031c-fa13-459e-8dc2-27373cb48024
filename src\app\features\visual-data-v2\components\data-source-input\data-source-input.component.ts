import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { PlotService } from '../../services/plot.service';
import { Folder } from '../../../../_models/visual-data/plot.model';
import { MatTreeModule, MatTreeNestedDataSource } from '@angular/material/tree';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { NestedTreeControl } from '@angular/cdk/tree';
import { DataviewService } from '../../../data-views/services/data-view.service';

interface TreeNode {
  id: string;
  name: string;
  children?: TreeNode[];
  created_date?: string;
}

export interface SelectionInfo {
  name: string | null;
  id: string | null;
  type?: string;
}

@Component({
  selector: 'app-data-source-input',
  imports: [MatTreeModule, MatButtonModule, MatIconModule],
  templateUrl: './data-source-input.component.html',
  styleUrl: './data-source-input.component.css',
})
export class DataSourceInputComponent implements OnInit {
  @Output() selectionEmitted = new EventEmitter<SelectionInfo>();

  plotService = inject(PlotService);
  dataviewService = inject(DataviewService);

  folders: Folder[] = [];
  hierarchicalFileFolderStruct: TreeNode[] = [];
  hasNextList: string | null = null;
  hasPreviousList: string | null = null;
  selection: SelectionInfo = {
    name: null,
    id: null,
    type: undefined,
  };
  isFileListVisible = false;
  loading = false;

  nestedDataSource = new MatTreeNestedDataSource<TreeNode>();
  nestTreeControl = new NestedTreeControl<TreeNode>(node => node.children);

  async ngOnInit() {
    this.loadFileList();
  }

  hasNestedChild = (_: number, node: TreeNode) =>
    !!node.children && node.children.length > 0;

  loadFileList(): void {
    const project_id = Number(localStorage.getItem('project_id'));
    this.plotService.plotFileList(project_id).subscribe({
      next: response => {
        if (response.status === 'success') {
          this.hasNextList = response.pagination?.next ?? null;
          this.hasPreviousList = response.pagination?.previous ?? null;
          this.buildHierarchy(response.data);
          this.nestedDataSource.data = this.hierarchicalFileFolderStruct;
        }
      },
      error: error => {
        console.error('Error fetching plot data', error);
      },
    });
  }

  //TO-DO : why different API to fetch the next page data???
  fetchPageData(url: string): void {
    this.plotService.getNextPageByUrl(url).subscribe({
      next: response => {
        if (response.status === 'success') {
          this.hasNextList = response.pagination?.next ?? null;
          this.hasPreviousList = response.pagination?.previous ?? null;
          this.buildHierarchy(response.data);
          this.nestedDataSource.data = this.hierarchicalFileFolderStruct;
        } else {
          console.error('Error retrieving data: ' + response.message);
        }
      },
      error: error => {
        console.error('Error fetching data:', error);
        if (error.status === 401 && error.statusText === 'Unauthorized') {
          console.error('Token expired, please login again!');
        }
      },
    });
  }

  // Function to load the next set of files
  loadNextFiles(): void {
    if (this.hasNextList) {
      this.fetchPageData(this.hasNextList);
    }
  }

  // Function to load the previous set of files
  loadPreviousFiles(): void {
    if (this.hasPreviousList) {
      this.fetchPageData(this.hasPreviousList);
    }
  }

  buildHierarchy(apiFoldersResponse: Folder[]): void {
    this.hierarchicalFileFolderStruct = apiFoldersResponse.map(folder =>
      this.buildTreeHierarchyFromRoot(folder),
    );
  }

  private buildTreeHierarchyFromRoot(folder: Folder): TreeNode {
    // Create base folder node
    const node: TreeNode = {
      id: String(folder.folder_id),
      name: folder.folder_name,
      children: [],
    };

    // Add subfolders recursively
    if (folder.subfolders && folder.subfolders.length > 0) {
      for (const subfolder of folder.subfolders) {
        const subfolderNode = this.buildTreeHierarchyFromRoot(subfolder);
        node.children?.push(subfolderNode);
      }
    }

    // Add files as leaf nodes
    if (folder.files && folder.files.length > 0) {
      const fileNodes = folder.files.map(file => ({
        id: String(file.file_id),
        name: file.file_name,
        created_date: file.created_date,
      }));
      node.children?.push(...fileNodes);
    }

    return node;
  }

  onFileSelect(node: TreeNode) {
    this.selection.name = node.name;
    this.selection.id = node.id;
    if (node.children && node.children.length > 0) {
      this.selection.type = 'folder';
    } else {
      this.selection.type = 'file';
    }

    this.isFileListVisible = false;
    this.selectionEmitted.emit(this.selection);
  }

  toggleFileList() {
    this.isFileListVisible = !this.isFileListVisible;
  }
}
