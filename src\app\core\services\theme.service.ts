import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { AppState } from '../store/app.state';
import { ThemeMode } from '../store/states/theme.state';
import { toggleTheme, setTheme } from '../store/actions/theme.action';
import { selectThemeMode } from '../store/selectors/theme.reducer';

/**
 * HYBRID THEME SERVICE
 *
 * This service implements a hybrid theming approach that combines:
 *
 * 1. ANGULAR THEMING API (Automatic Material Component Theming)
 *    - All Material components (mat-button, mat-card, etc.) get automatic theming
 *    - Handled via CSS mixins in styles.scss: @include mat.all-component-colors($dark-theme)
 *    - Provides consistent Material Design aesthetics with proper hover/focus states
 *
 * 2. CSS CLASS-BASED THEMING (Custom Component Theming)
 *    - Custom components, Tailwind classes, third-party libraries
 *    - Handled via .light-mode and .dark-mode CSS classes
 *    - Provides complete control and framework independence
 *
 * 3. STATE MANAGEMENT & PERSISTENCE
 *    - NgRx store integration for reactive theme updates
 *    - localStorage persistence for user preferences
 *    - System preference detection for initial theme
 */
@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  private readonly THEME_STORAGE_KEY = 'user-theme-preference';

  constructor(
    private store: Store<AppState>,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: object,
  ) {
    // Initialize theme from localStorage or system preference
    this.initializeTheme();

    // Subscribe to theme changes and apply them
    this.store.select(selectThemeMode).subscribe(themeMode => {
      this.applyTheme(themeMode);
      this.saveThemePreference(themeMode);
    });
  }

  /**
   * Get the current theme mode as an observable
   */
  get currentTheme$(): Observable<ThemeMode> {
    return this.store.select(selectThemeMode);
  }

  /**
   * Toggle between light and dark theme
   */
  toggleTheme(): void {
    this.store.dispatch(toggleTheme());
  }

  /**
   * Set a specific theme mode
   */
  setTheme(themeMode: ThemeMode): void {
    this.store.dispatch(setTheme({ themeMode }));
  }

  /**
   * Initialize theme based on user preference or system preference
   */
  private initializeTheme(): void {
    // Only initialize theme in browser environment
    if (!isPlatformBrowser(this.platformId)) {
      // Default to light theme on server
      this.store.dispatch(setTheme({ themeMode: 'light' }));
      return;
    }

    const savedTheme = this.getSavedThemePreference();

    if (savedTheme) {
      this.store.dispatch(setTheme({ themeMode: savedTheme }));
    } else {
      // Check system preference
      const prefersDark = window.matchMedia(
        '(prefers-color-scheme: dark)',
      ).matches;
      const systemTheme: ThemeMode = prefersDark ? 'dark' : 'light';
      this.store.dispatch(setTheme({ themeMode: systemTheme }));
    }
  }

  /**
   * Apply theme to the DOM
   */
  private applyTheme(themeMode: ThemeMode): void {
    const body = this.document.body;

    // Remove existing theme classes
    body.classList.remove('light-mode', 'dark-mode');

    // Add the new theme class for custom components
    body.classList.add(`${themeMode}-mode`);
  }

  /**
   * Save theme preference to localStorage
   */
  private saveThemePreference(themeMode: ThemeMode): void {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem(this.THEME_STORAGE_KEY, themeMode);
    }
  }

  /**
   * Get saved theme preference from localStorage
   */
  private getSavedThemePreference(): ThemeMode | null {
    if (!isPlatformBrowser(this.platformId)) {
      return null;
    }

    const saved = localStorage.getItem(this.THEME_STORAGE_KEY);
    return saved === 'light' || saved === 'dark' ? saved : null;
  }
}
