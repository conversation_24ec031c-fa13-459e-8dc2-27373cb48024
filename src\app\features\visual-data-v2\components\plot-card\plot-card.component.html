<!-- PLEASE DONT REMOVE THE BELOW DIV, id = plot-graph is used for full-screen and it needs to be in div not inside angular component -->
<div
  class="h-full flex flex-col rounded-xl"
  [attr.id]="'plot-graph-' + plotData.id">
  <div class="h-14">
    <app-plot-card-header [plotId]="plotData.id"></app-plot-card-header>
  </div>

  <div class="overflow-auto flex-1">
    <plotly-plot
      [data]="plotData.json_data.data"
      [layout]="plotData.json_data.layout"
      [useResizeHandler]="true"
      [config]="{ responsive: true }"
      [style]="{ position: 'relative', width: '100%', height: '100%' }"
      class="bg-white dark:bg-gray-900"></plotly-plot>
  </div>
</div>
