<div>
  <input
    matInput
    placeholder="Select a file"
    [value]="this.selection.name"
    (click)="toggleFileList()"
    class="outline-none bg-transparent flex-1 border px-3 py-2 shadow-sm bg-white w-full h-12 rounded-md"
    readonly />
</div>

@if (this.isFileListVisible) {
  <div>
    @if (this.hasPreviousList) {
      <div
        class="file-select-option"
        (click)="loadPreviousFiles()"
        (keyup.enter)="loadPreviousFiles()"
        tabindex="0">
        Load previous files...
      </div>
    }
    <mat-tree
      class="example-tree mat-elevation-z4"
      [dataSource]="nestedDataSource"
      [treeControl]="nestTreeControl">
      <mat-tree-node matTreeNodeToggle *matTreeNodeDef="let node">
        <div
          class="cursor-pointer"
          (click)="onFileSelect(node)"
          (keydown.enter)="onFileSelect(node)"
          tabindex="0">
          {{ node.name }}
        </div>
      </mat-tree-node>

      <mat-nested-tree-node *matTreeNodeDef="let node; when: hasNestedChild">
        <div class="mat-tree-node">
          <button mat-icon-button matTreeNodeToggle>
            <mat-icon>
              {{
                nestTreeControl.isExpanded(node)
                  ? 'expand_more'
                  : 'chevron_right'
              }}
            </mat-icon>
          </button>
          {{ node.name }}
        </div>
        <div
          class="nested-node"
          [class.example-tree-invisible]="nestTreeControl.isExpanded(node)">
          <ng-container matTreeNodeOutlet></ng-container>
        </div>
      </mat-nested-tree-node>
    </mat-tree>
    @if (this.hasNextList) {
      <div
        class="file-select-option"
        (click)="loadNextFiles()"
        (keyup.enter)="loadNextFiles()"
        tabindex="0">
        Load more files...
      </div>
    }
  </div>
}
