/* ========================================
   THEME-AWARE UTILITY CLASSES
   Using --md-sys-* tokens for custom components (from _color.scss)
   ======================================== */

// Background utilities - semantic naming
.bg-page {
  background-color: var(--md-sys-color-surface);
}

.bg-card {
  background-color: var(--md-sys-color-surface-container);
}

.bg-elevated {
  background-color: var(--md-sys-color-surface-container-high);
}

.bg-button-primary {
  background-color: var(--md-sys-color-primary);
}

.bg-button-secondary {
  background-color: var(--md-sys-color-surface-container-highest);
}

// Text color utilities - semantic naming
.text-primary {
  color: var(--md-sys-color-on-surface);
}

.text-secondary {
  color: var(--md-sys-color-on-surface-variant);
}

.text-accent {
  color: var(--md-sys-color-primary);
}

.text-on-primary {
  color: var(--md-sys-color-on-primary);
}

.text-on-dark {
  color: var(--md-sys-color-on-primary-container);
}

// Border utilities - semantic naming
.border-subtle {
  border-color: var(--md-sys-color-outline-variant);
}

.border-normal {
  border-color: var(--md-sys-color-outline);
}

// Icon color utilities - semantic naming
.icon-accent {
  color: var(--md-sys-color-primary);
}

.icon-primary {
  color: var(--md-sys-color-on-surface);
}

.icon-secondary {
  color: var(--md-sys-color-on-surface-variant);
}

.icon-on-primary {
  color: var(--md-sys-color-on-primary);
}

/* ========================================
   HEADER ICON BUTTON UTILITY
   ======================================== */

/* Base header icon button styling */
.header-icon-button {
  color: var(--md-sys-color-primary);
  background-color: var(--md-sys-color-surface-container);

  &:hover {
    background-color: var(--md-sys-color-surface-container-high);
  }
}
