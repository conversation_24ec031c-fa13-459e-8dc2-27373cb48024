/* ========================================
   STREAMLINED UTILITY CLASSES
   Using Angular Material's generated --mat-sys-* tokens for consistency
   ======================================== */

// Background utilities - semantic naming
.bg-page {
  background-color: var(--mat-sys-surface);
}

.bg-card {
  background-color: var(--mat-sys-surface-container);
}

.bg-elevated {
  background-color: var(--mat-sys-surface-container-high);
}

.bg-button-primary {
  background-color: var(--mat-sys-primary);
}

.bg-button-secondary {
  background-color: var(--mat-sys-surface-container-highest);
}

// Text color utilities - semantic naming
.text-primary {
  color: var(--mat-sys-on-surface);
}

.text-secondary {
  color: var(--mat-sys-on-surface-variant);
}

.text-accent {
  color: var(--mat-sys-primary);
}

.text-on-primary {
  color: var(--mat-sys-on-primary);
}

.text-on-dark {
  color: var(--mat-sys-on-primary-container);
}

// Border utilities - semantic naming
.border-subtle {
  border-color: var(--mat-sys-outline-variant);
}

.border-normal {
  border-color: var(--mat-sys-outline);
}

// Icon color utilities - semantic naming
.icon-accent {
  color: var(--mat-sys-primary);
}

.icon-primary {
  color: var(--mat-sys-on-surface);
}

.icon-secondary {
  color: var(--mat-sys-on-surface-variant);
}

.icon-on-primary {
  color: var(--mat-sys-on-primary);
}

/* ========================================
   HEADER ICON BUTTON UTILITY
   ======================================== */

/* Base header icon button styling */
.header-icon-button {
  color: var(--mat-sys-primary);

  background-color: var(--mat-sys-surface-container);

  &:hover {
    background-color: var(--mat-sys-surface-container-high);
  }
}
