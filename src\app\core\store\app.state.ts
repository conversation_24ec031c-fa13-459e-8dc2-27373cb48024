import { ActionReducerMap } from '@ngrx/store';
import { themeReducer } from './reducers/theme.reducer';
import { ThemeState } from './states/theme.state';
import { tabReducer } from './reducers/tab.reducer';
import { TabState } from './states/tab.state';

export const appStore: ActionReducerMap<AppState> = {
  theme: themeReducer,
  tab: tabReducer,
};

export const AppStoreEffects = [];

export interface AppState {
  theme: ThemeState;
  tab: TabState;
}
