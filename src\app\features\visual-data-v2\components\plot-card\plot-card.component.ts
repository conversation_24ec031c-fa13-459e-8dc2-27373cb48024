import { Component, Input, AfterViewInit } from '@angular/core';
import { PlotDataUI } from '../../models/plot.model';
import { PlotlyViaCDNModule } from 'angular-plotly.js';
import { PlotCardHeaderComponent } from '../plot-card-header/plot-card-header.component';

@Component({
  selector: 'app-plot-card',
  imports: [
    PlotlyViaCDNModule,
    PlotCardHeaderComponent,
    PlotCardHeaderComponent,
  ],
  templateUrl: './plot-card.component.html',
  styleUrl: './plot-card.component.css',
})
export class PlotCardComponent implements AfterViewInit {
  @Input({ required: true }) plotData!: PlotDataUI;
  ngAfterViewInit(): void {
    window.dispatchEvent(new Event('resize')); // For plotly to adjust the layout after the view is initialized
  }
}
