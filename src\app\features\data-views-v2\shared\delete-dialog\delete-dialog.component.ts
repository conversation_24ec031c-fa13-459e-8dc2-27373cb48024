import { Component, inject, Input, OnInit } from '@angular/core';
import { g_const } from '../../../../_utility/global_const';
import {
  MAT_DIALOG_DATA,
  MatDialogConfig,
  MatDialogRef,
} from '@angular/material/dialog';
import { SharedModule } from '../../../../shared/shared.module';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-delete-dialog',
  imports: [SharedModule, MatCardModule, MatIconModule, MatButtonModule],
  templateUrl: './delete-dialog.component.html',
  styleUrl: './delete-dialog.component.css',
})
export class DeleteDialogComponent implements OnInit {
  g_const = g_const;
  @Input() isModalOpen = false;
  @Input() projectTitle = '';
  modalTitle = '';
  readonly dialogRef = inject(MatDialogRef<DeleteDialogComponent>);
  static viewConfig: MatDialogConfig = {
    maxWidth: '100%',
    width: '440px',
    disableClose: true,
    maxHeight: '100%',
    height: '276px',
  };
  readonly data = inject<{ type: string }>(MAT_DIALOG_DATA);

  ngOnInit(): void {
    this.modalTitle = this.data.type;
  }

  cancel(): void {
    this.dialogRef.close();
  }

  delete(): void {
    this.dialogRef.close(true);
  }
}
