import {
  Component,
  ChangeDetectionStrategy,
  inject,
  ChangeDetectorRef,
  effect,
  AfterViewInit,
} from '@angular/core';

import { MatTreeModule } from '@angular/material/tree';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../../../shared/shared.module';
import { MatExpansionModule } from '@angular/material/expansion';
import { DataViewComponentStore } from '../../component-store/data-view.store';
import { Router } from '@angular/router';
import { FlatTreeControl } from '@angular/cdk/tree';
import {
  DynamicDatabase,
  DynamicDataSource,
  DynamicFlatNode,
} from './data-view-class';
import { MatDialog, MatDialogConfig } from '@angular/material/dialog';
import { DeleteDialogComponent } from '../delete-dialog/delete-dialog.component';
import { EmptyStateCardComponent } from '../../../../shared/components/empty-state-card/empty-state-card.component';

@Component({
  selector: 'app-data-view-tree',
  standalone: true,
  imports: [
    CommonModule,
    SharedModule,
    MatExpansionModule,
    MatIconModule,
    MatTreeModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    EmptyStateCardComponent,
  ],
  templateUrl: './data-view-tree.component.html',
  styleUrl: './data-view-tree.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataViewTreeComponent implements AfterViewInit {
  private store = inject(DataViewComponentStore);
  readonly dialog = inject(MatDialog);

  treeControl!: FlatTreeControl<DynamicFlatNode>;
  isLoading = false;
  dataSource!: DynamicDataSource;
  currentPath = '';
  isEmpty = false;
  static viewConfig: MatDialogConfig = {
    maxWidth: '100%',
    width: '860px',
    disableClose: true,
    maxHeight: '100%',
    height: '560px',
  };
  showFilePreview = false;
  searchFilter = false;

  constructor(
    private cdr: ChangeDetectorRef,
    private router: Router,
  ) {
    const database = inject(DynamicDatabase);
    this.treeControl = new FlatTreeControl<DynamicFlatNode>(
      this.getLevel,
      this.isExpandable,
    );
    const hierarchyData = this.store.hierarchy()[this.store.project_id()];
    this.dataSource = new DynamicDataSource(this.treeControl, database);
    this.dataSource.data = database.initialData(hierarchyData);
    this.dataSource.data[0].item.name = this.store.projectTitle();
    this.isEmpty = hierarchyData.length > 0 ? false : true;
    cdr.markForCheck();
  }

  ngAfterViewInit(): void {
    this.isLoading = this.store.isLoading();
    setTimeout(() => {
      const firstLevelNodes = this.treeControl.dataNodes.filter(
        node => node.level === 0,
      );
      firstLevelNodes.forEach(node => this.treeControl.expand(node));
    });
  }
  onEffect = effect(() => {
    this.searchFilter = this.store.search() === '' ? false : true;
    this.currentPath =
      this.store.selectedFolderPath() === ''
        ? ''
        : `/${this.store.selectedFolderPath()}`;
    this.cdr.markForCheck();
  });
  getLevel = (node: DynamicFlatNode) => node.level;

  isExpandable = (node: DynamicFlatNode) => node.expandable;
  hasChild = (_: number, _nodeData: DynamicFlatNode) => _nodeData.expandable;
  isLoadMore = (_: number, node: DynamicFlatNode) =>
    node.item.type == 'loadMore';

  goToViewFile(id: number) {
    this.router.navigate([
      `/project/${this.store.project_id()}/data-view/${id}/file`,
    ]);
  }

  deleteFile(id: number) {
    const dialogRef = this.dialog.open(DeleteDialogComponent, {
      ...DeleteDialogComponent.viewConfig,
      data: { type: 'file' },
    });
    dialogRef.afterClosed().subscribe((allowDelete: boolean) => {
      if (allowDelete) this.store.deleteFile(id, this.store.project_id());
    });
  }

  deleteFolder(path: string) {
    const dialogRef = this.dialog.open(DeleteDialogComponent, {
      ...DeleteDialogComponent.viewConfig,
      data: { type: 'folder' },
    });
    dialogRef.afterClosed().subscribe((allowDelete: boolean) => {
      if (allowDelete)
        this.store.deleteFolder(
          path.startsWith('/') ? path.slice(1) : path,
          this.store.project_id(),
        );
    });
  }

  allowFolderDelete(node: DynamicFlatNode) {
    return node.item.name == this.store.projectTitle() ? false : true;
  }

  loadNextPage(node: DynamicFlatNode) {
    this.dataSource.loadMore(node);
  }

  showPreview(id: number) {
    this.showFilePreview = !this.showFilePreview;
    this.store.setPreviewFileId(id, this.showFilePreview);
  }
}
