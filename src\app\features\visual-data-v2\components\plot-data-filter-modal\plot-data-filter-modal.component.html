@if (this.isLoading) {
  <div class="flex justify-center items-center p-5">
    <mat-spinner></mat-spinner>
  </div>
} @else {
  <form
    [formGroup]="this.filterPlotDataForm"
    (ngSubmit)="onSubmit()"
    class="plot-filter-modal">
    <!-- HEADER -->
    <div>
      <div
        class="flex items-center justify-between p-2 border-gray-200 dark:border-gray-700">
        <h2 mat-dialog-title>Filter Data</h2>
        <button
          mat-icon-button
          type="button"
          (click)="closeModal()"
          class="text-gray-400 hover:text-gray-600">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <hr />
    </div>

    <div formArrayName="columnFilters" mat-dialog-content>
      <!-- labels -->
      <div class="grid grid-cols-2 gap-2 mb-2">
        <div>
          <h3>Colums</h3>
        </div>
        <div class="grid grid-cols-5 gap-2">
          <h3 class="col-span-2">Operator</h3>
          <h3 class="col-span-2">Value</h3>
          <div></div>
        </div>
      </div>

      @for (
        filter of this.filterPlotDataForm.controls.columnFilters.controls;
        track columnFilterIndex;
        let columnFilterIndex = $index
      ) {
        <!-- Represent single row -->
        <div
          class="grid grid-cols-2 gap-2 mb-2"
          [formGroupName]="columnFilterIndex">
          <!-- ROW ELEMENT : 1  GROUP LOGICAL OPRRATOR AND COLUMN HEADER SELECT -->
          <div class="flex w-full gap-2 items-start">
            <!-- LOGIC OPERATOR -->
            @if (columnFilterIndex !== 0) {
              <div class="flex-none w-[20%] min-w-[120px]">
                <mat-form-field class="w-full">
                  <!-- <mat-label>Logic</mat-label> -->
                  <mat-select formControlName="logicOperator">
                    <mat-option value="AND">AND</mat-option>
                    <mat-option value="OR">OR</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            }

            <!-- COLUMN HEADER -->
            <div class="flex-grow">
              <mat-form-field class="w-full">
                <!-- <mat-label>Column</mat-label> -->
                <mat-select
                  formControlName="columnHeader"
                  [placeholder]="'Select Column'">
                  @for (choice of columnChoicesTransformed; track $index) {
                    @if (choice[1].length > 0) {
                      <mat-optgroup [label]="choice[0]">
                        @for (option of choice[1]; track $index) {
                          <mat-option [value]="option">{{ option }}</mat-option>
                        }
                      </mat-optgroup>
                    }
                  }
                </mat-select>
              </mat-form-field>
            </div>
          </div>

          <!-- ROW ELEMENT : 2 GROUP OPERATOR, INPUT, REMOVE FILTER-->
          <div class="grid grid-cols-5 gap-2">
            <!-- OPERATOR SELECT -->
            <mat-form-field class="col-span-2">
              <!-- <mat-label>Operator</mat-label> -->
              <mat-select
                formControlName="operator"
                [placeholder]="'Select Operator'">
                @let columnHeader = filter.get('columnHeader')?.value;
                @if (columnHeader !== null && columnHeader !== undefined) {
                  @for (
                    operator of this.getOperatorBasedSelectedColumnHeader(
                      columnHeader
                    );
                    track $index
                  ) {
                    <mat-option [value]="operator">
                      {{ operator }}
                    </mat-option>
                  }
                }
              </mat-select>
            </mat-form-field>

            <!-- INPUT  -->
            <div [formGroupName]="'value'" class="col-span-2">
              <!-- INPUT TYPE : BETWEEN -->
              @if (filter.get('operator')?.value === 'between') {
                <div class="grid grid-cols-2 gap-2">
                  <mat-form-field appearance="fill">
                    <mat-label>Min</mat-label>
                    <input matInput type="number" formControlName="minValue" />
                  </mat-form-field>

                  <mat-form-field appearance="fill">
                    <mat-label>Max</mat-label>
                    <input matInput type="number" formControlName="maxValue" />
                  </mat-form-field>
                </div>
              } @else {
                <!-- INPUT TYPE : SINGLE VALUE -->
                <mat-form-field appearance="fill" class="h-full w-full">
                  <input
                    type="text"
                    matInput
                    formControlName="singleValue"
                    [matAutocomplete]="auto"
                    [placeholder]="'Enter Value'" />
                  <mat-autocomplete #auto="matAutocomplete">
                    @if (filter.get('columnHeader')?.value) {
                      @for (
                        option of this._getOptionsforCatergoricalColumn(
                          filter.get('columnHeader')?.value || ''
                        );
                        track $index
                      ) {
                        <mat-option [value]="option">
                          {{ option }}
                        </mat-option>
                      }
                    }
                  </mat-autocomplete>
                </mat-form-field>
              }
            </div>

            <!-- REMOVE FILTER BUTTON -->
            <div class="col-span-1 flex justify-center items-center">
              <button
                [disabled]="filter.get('isExistingFilter')?.value"
                (click)="removeColumnFilter(columnFilterIndex)"
                type="button"
                class="flex items-center">
                <mat-icon>delete_outline</mat-icon>
              </button>
            </div>
          </div>
        </div>
      }

      <button
        (click)="addColumnFilter()"
        type="button"
        class="border-[1px] rounded-full flex items-center !border-gray-300 space-x-1 p-3 !bg-transparent !text-txt-color">
        <mat-icon>add</mat-icon>
      </button>
    </div>
    <div mat-dialog-actions class="flex justify-start gap-2 mt-4">
      <button mat-flat-button type="submit">save</button>
      <button
        mat-flat-button
        (click)="removeAllFiltersFromPlot()"
        [disabled]="this.data.filterInstanceId === null"
        type="button">
        <mat-icon>restart_alt</mat-icon>
        Remove Filters
      </button>
    </div>
  </form>
}
