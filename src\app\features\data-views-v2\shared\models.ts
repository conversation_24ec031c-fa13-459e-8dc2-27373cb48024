export const SortOptions = [
  {
    title: 'Data Type',
    value: 'file_type',
  },
  {
    title: 'Upload Date',
    value: 'created_at',
  },
  {
    title: 'File Name',
    value: 'files',
  },
];
export enum SortTitle {
  file_type = 'Data Type',
  created_at = 'Upload Date',
  files = 'File Name',
}

export const DataViewUploadTableHeading = ['File', 'Size', 'Actions'];
export const MIME_TYPE_MAPPING: Record<string, string> = {
  csv: 'text/csv',
  parquet: 'application/json',
  xml: 'application/xml',
  yaml: 'application/x-yaml',
  yml: 'application/x-yaml',
  tsv: 'text/tab-separated-values',
  txt: 'text/plain',
  png: 'image/png',
  jpeg: 'image/jpeg',
  jpg: 'image/jpeg',
};
export enum FileIcon {
  CSV = 'table_chart',
  PARQUET = 'data_object',
  DEFAULT = 'insert_drive_file',
  IMAGE = 'image_outline',
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
