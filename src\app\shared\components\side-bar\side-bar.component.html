<div class="flex flex-col h-full">
  <button mat-icon-button (click)="toggleDrawer()" *ngIf="isSmallScreen">
    <mat-icon>menu</mat-icon>
  </button>
  <button
    mat-icon-button
    class="hamburger-btn"
    (click)="closetoggleDrawer()"
    *ngIf="crossScreen">
    <mat-icon>close</mat-icon>
  </button>

  <mat-drawer-container
    class="example-container flex-1 rounded-lg w-16 sidebar-container m-1"
    *ngIf="!isSmallScreen">
    <mat-drawer
      #drawer
      [mode]="drawerMode"
      [(opened)]="isDrawerOpened"
      class="shadow-2xl sidebar-container">
      <div class="flex flex-col justify-between h-full">
        <div class="mt-4 w-20">
          <a
            [routerLink]="['/dashboard/projects']"
            (click)="handleButtonClick('projects')"
            class="ml-3 cursor-pointer inline-flex items-center">
            <img
              src="../../../assets/Logo.png"
              alt="Logo"
              class="h-[38px] gap-3 object-contain" />
          </a>
          <div class="border-t-2 gap-2 mt-4 ml-1 w-14 sidebar-divider"></div>
          <div>
            <button *ngIf="showSidebar" (click)="goBack()" class="w-16 h-16">
              <mat-icon class="m-4 sidebar-icon">arrow_back</mat-icon>
            </button>
            <!-- Disable Data version and training feature for V1 -->
            @for (tab of settingsTabs; track tab) {
              <button
                id="step2-2"
                class="rounded-2xl w-16 h-16 mt-2 border-8"
                [ngClass]="{
                  'light-sidebar': activeButton === tab.name,
                  'border-transparent': activeButton !== tab.name,
                }"
                *ngIf="showSidebar"
                (click)="handleButtonClick(tab.name)"
                [matTooltip]="tab.name | titlecase"
                matTooltipClass="sidebar-tooltip">
                <mat-icon
                  class="m-3 sidebar-icon"
                  [ngClass]="{
                    'active': activeButton === tab.name
                  }"
                  >{{ tab.icon }}</mat-icon
                >
              </button>
            }
          </div>
        </div>
        <div>
          <button
            class="rounded-2xl w-16 h-16 mt-2 border-8"
            *ngIf="!showSidebar"
            [ngClass]="{
              'light-sidebar': activeButton === 'settings',
              'border-transparent': activeButton !== 'settings',
            }"
            (click)="handleButtonClick('settings')"
            matTooltip="Settings"
            matTooltipClass="sidebar-tooltip">
            <mat-icon
              class="m-3 sidebar-icon"
              [ngClass]="{
                'active': activeButton === 'settings'
              }"
              >settings_outline</mat-icon
            >
          </button>
          <div class="border-t-2 ml-1 w-14 sidebar-divider"></div>
          <button matTooltip="Logout" matTooltipClass="sidebar-tooltip">
            <mat-icon (click)="logout()" class="m-5 sidebar-icon">logout</mat-icon>
          </button>
        </div>
      </div>
    </mat-drawer>

    <mat-drawer-content>
      <div
        class="flex flex-col h-full justify-between rounded-xl overflow-hidden sidebar-container"
        style="box-shadow: 0px 4px 24px 0px rgba(25, 47, 65, 0.05)">
        <div class="mt-4 w-20">
          <img
            src="../../../assets/Logo.png"
            alt="Logo"
            class="h-[38px] w-14 gap-3 object-contain" />
        </div>
        <div>
          <div class="w-20">
            <!-- <mat-icon class="m-5" (click)="handleButtonClick('settings')" >settings</mat-icon> -->
          </div>
          <div class="border-t-2 m-1 w-14 sidebar-divider"></div>

          <div class="w-20">
            <button
              class="rounded-xl w-16 h-16 mt-2 border-8"
              [ngClass]="{
                'light-sidebar': activeButton === 'settings',
                'border-transparent': activeButton !== 'settings',
              }"
              *ngIf="showSidebar"
              (click)="handleButtonClick('settings')"
              matTooltip="Settings"
              matTooltipClass="sidebar-tooltip">
              <mat-icon
                class="m-3 sidebar-icon"
                [ngClass]="{
                  'active': activeButton === 'settings'
                }"
                >settings_outline</mat-icon
              >
            </button>
            <mat-icon
              class="m-5 sidebar-icon"
              (click)="logout()"
              matTooltip="Logout"
              matTooltipClass="sidebar-tooltip"
              >logout</mat-icon
            >
          </div>
        </div>
      </div>
    </mat-drawer-content>
  </mat-drawer-container>
</div>
