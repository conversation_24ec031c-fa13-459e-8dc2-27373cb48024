import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { DataViewUploadTableHeading, FileIcon } from '../../../shared/models';
import { UploadedFile } from '../../../shared/data-view.interfaces';

@Component({
  selector: 'app-upload-files-table',
  imports: [MatIconModule, MatButtonModule],
  templateUrl: './upload-files-table.component.html',
  styleUrl: './upload-files-table.component.css',
})
export class UploadFilesTableComponent {
  @Input() uploadedFilesList: UploadedFile[] = [];
  @Output() fileRemoved = new EventEmitter<number>();
  tableHeading = DataViewUploadTableHeading;

  openFileSelector() {
    console.log('open fileselecteor');
  }

  getFileIcons(file: File): string {
    if (file.name.endsWith('.csv')) return FileIcon.CSV;
    else if (file.name.endsWith('.parquet')) return FileIcon.PARQUET;
    else if (file.type.startsWith('image/')) return FileIcon.IMAGE;
    else return FileIcon.DEFAULT;
  }

  removeFile(index: number) {
    this.fileRemoved.emit(index);
  }

  trackByIndex(index: number): number {
    return index;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
