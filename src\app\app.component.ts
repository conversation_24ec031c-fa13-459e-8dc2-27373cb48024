import {
  Component,
  Inject,
  OnInit,
  PLATFORM_ID,
  ViewEncapsulation,
} from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { NgxFileDropModule } from 'ngx-file-drop';
import { PlotlyViaCDNModule } from 'angular-plotly.js';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, NgxFileDropModule],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class AppComponent implements OnInit {
  title = 'AICU';

  constructor(@Inject(PLATFORM_ID) private platformId: object) {}

  async ngOnInit(): Promise<void> {
    // Ensure this code only runs in the browser environment
    if (isPlatformBrowser(this.platformId)) {
      PlotlyViaCDNModule.setPlotlyVersion('1.55.2', 'plotly'); // Configure Plotly CDN version
      PlotlyViaCDNModule.setPlotlyBundle(null);
    }
  }
}
