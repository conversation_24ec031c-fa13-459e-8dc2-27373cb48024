@if (isTableVisible || isImageVisible) {
  <div
    class="overflow-y-auto hover:overflow-y-auto overflow-hidden ml-6 table-modal rounded-lg border-gray-400 shadow-lg max-h-[80vh] min-w-[25vw] relative">
    @if (previewLoading) {
      <div
        class="absolute inset-0 flex justify-center items-center bg-white bg-opacity-80 backdrop-blur-sm z-10">
        <app-loader [loading]="previewLoading"></app-loader>
      </div>
    }
    <p class="font-medium">Preview</p>

    @if (isTableVisible) {
      <section
        class="example-container mat-elevation-z8 min-w-[25vw] border border-[#ecebf8] rounded-md"
        tabindex="0">
        <table mat-table [dataSource]="dataSource" class="min-w-[100%]">
          <ng-container
            *ngFor="let column of displayedColumns"
            [matColumnDef]="column">
            <th mat-header-cell *matHeaderCellDef class="">
              {{ column | titlecase }}
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element[column] }}
            </td>
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </section>
    }

    @if (isImageVisible) {
      <section class="mat-elevation-z8" tabindex="0" *ngIf="isImageVisible">
        <img
          [src]="fileMetadata.safeFileUrl"
          alt="File Preview"
          class="h-[400px] object-contain" />
      </section>
    }

    <div>
      <p class="font-medium mt-4">
        {{ fileMetadata.name | capitalize | replaceUnderscore }}
      </p>
      <p class="m-0" *ngIf="isTableVisible">
        {{ fileMetadata.totalColumns }} columns,
        {{ fileMetadata.totalRows }} rows
      </p>
      <p class="m-0">File Size: {{ fileMetadata.size }}</p>
      <p *ngIf="isTableVisible" class="mt-4">Columns</p>

      @if (isTableVisible) {
        <span *ngIf="isTableVisible">
          <mat-chip-set class="m-0 gap-2">
            <mat-chip *ngFor="let column of displayedColumns.slice(0, 6)">
              {{ column }}
            </mat-chip>
            <ng-container *ngIf="displayedColumns.length > 6 && !showAllChips">
              <mat-chip (click)="showAllChips = true" class="cursor-pointer">
                +{{ displayedColumns.length - 6 }} more
              </mat-chip>
            </ng-container>
            <ng-container *ngIf="showAllChips">
              <mat-chip *ngFor="let column of displayedColumns.slice(6)">
                {{ column }}
              </mat-chip>
            </ng-container>
          </mat-chip-set>
        </span>
      }

      @if (isImageVisible) {
        <p *ngIf="isImageVisible">{{ fileMetadata.resolution }}</p>
      }
    </div>
  </div>
}
