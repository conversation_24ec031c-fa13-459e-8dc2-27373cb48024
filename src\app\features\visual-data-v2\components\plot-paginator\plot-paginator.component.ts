import { Component, computed, inject } from '@angular/core';
import { visualDataStore } from '../../store/visual-data.store';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-plot-paginator',
  imports: [MatIconModule, MatButtonModule],
  templateUrl: './plot-paginator.component.html',
  styleUrl: './plot-paginator.component.css',
})
export class PlotPaginatorComponent {
  visualDataStore = inject(visualDataStore);

  isNextPageAvailable = computed(() => {
    const currentCursor = this.visualDataStore.pagination().currentCursor;
    const totalPlots = this.visualDataStore.pagination().totalItems;
    const limit = this.visualDataStore.pagination().limit;
    return currentCursor + limit < totalPlots;
  });

  isPrevisousPageAvailable = computed(() => {
    const currentCursor = this.visualDataStore.pagination().currentCursor;
    return currentCursor > 0;
  });

  startItem = computed(() => {
    return this.visualDataStore.pagination().currentCursor + 1;
  });

  endItem = computed(() => {
    const { currentCursor, limit, totalItems } =
      this.visualDataStore.pagination();
    return Math.min(currentCursor + limit, totalItems);
  });

  gotoNextPage() {
    this.visualDataStore.loadNextPageOfPlots();
  }
  gotoPreviousPage() {
    this.visualDataStore.loadPreviousPageOfPlots();
  }
}
