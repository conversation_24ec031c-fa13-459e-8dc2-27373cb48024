.custom-dialog .mat-mdc-dialog-container {
  max-width: 90vw; /* Maximum width as 90% of viewport width */
  overflow-y: auto; /* Enable scrolling if needed */
}

.custom-dialog .mat-mdc-dialog-content {
  height: 600px; /* Set the height explicitly */
  overflow-y: auto; /* Enable scrolling */
}

.custom-dialog .mat-mdc-dialog-actions {
  justify-content: center; /* Center align dialog actions */
  padding: 16px; /* Add padding to actions */
}

:host ::ng-deep .active-setting-item {
  background-color: #296197 !important;
}

:host ::ng-deep .active-setting-item .mdc-list-item__primary-text {
  color: white !important;
}
