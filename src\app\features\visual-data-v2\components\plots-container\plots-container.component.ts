import { Component, computed, inject } from '@angular/core';
import { visualDataStore } from '../../store/visual-data.store';
import { PlotCardComponent } from '../plot-card/plot-card.component';
import { Subject } from 'rxjs';
import { debounceTime } from 'rxjs/operators';

import {
  CompactType,
  DisplayGrid,
  GridsterConfig,
  GridsterItem,
  GridsterModule,
  GridType,
} from 'angular-gridster2';

import { GridsterItemWithPlotly } from '../../models/plot.model';

import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { PlotPaginatorComponent } from '../plot-paginator/plot-paginator.component';

@Component({
  selector: 'app-plots-container',
  imports: [
    GridsterModule,
    PlotCardComponent,
    MatProgressSpinnerModule,
    MatIconModule,
    PlotPaginatorComponent,
  ],
  templateUrl: './plots-container.component.html',
  styleUrl: './plots-container.component.css',
})
export class PlotsContainerComponent {
  visualDataStore = inject(visualDataStore);
  private layoutChanged$ = new Subject<void>();
  private cachedQueueForDebouncedInputResizeChangedGridItem: GridsterItem[] =
    [];

  constructor() {
    // USED FOR DEBOUNCE LOGIC
    this.layoutChanged$.pipe(debounceTime(200)).subscribe(() => {
      this.onLayoutSettled();
    });
  }
  // USED FOR DEBOUNCE LOGIC
  private triggerLayoutChange() {
    this.layoutChanged$.next();
  }

  // USED FOR DEBOUNCE LOGIC
  private onLayoutSettled() {
    if (this.cachedQueueForDebouncedInputResizeChangedGridItem.length == 0) {
      return;
    }

    try {
      const changedItemLayoutPayload =
        this.cachedQueueForDebouncedInputResizeChangedGridItem.map(
          updatedGridItem => {
            return { ...updatedGridItem } as GridsterItemWithPlotly;
          },
        );
      this.visualDataStore.saveDisplayLayoutInDbAndSyncStoreLayoutWithGridster({
        projectId: Number(this.visualDataStore.projectData()?.id),
        changedItemLayout: changedItemLayoutPayload,
      });
      window.dispatchEvent(new Event('resize')); // For Plotly
    } catch (Error) {
      console.log(Error);
    } finally {
      this.cachedQueueForDebouncedInputResizeChangedGridItem = [];
    }
  }

  gridsterConfig = computed<GridsterConfig>(() => ({
    gridType: GridType.VerticalFixed,
    displayGrid: DisplayGrid.OnDragAndResize,
    compactType: CompactType.CompactUp,
    minCols: 3,
    maxCols: 3,
    minRows: 1,
    pushItems: true,
    pushResizeItems: true,
    swapWhileDragging: true,
    swap: true,
    resizable: {
      enabled: true,
      delayStart: 0,
    },
    draggable: {
      enabled: true,
      dragHandleClass: 'drag-handler',
      ignoreContent: true,
    },
    itemChangeCallback: item => {
      // USED FOR DEBOUNCE LOGIC
      this.cachedQueueForDebouncedInputResizeChangedGridItem.push(item);
      this.triggerLayoutChange();
    },
  }));
}
