import { Component, inject } from '@angular/core';
import { visualDataStore } from '../../store/visual-data.store';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { g_const } from '../../../../_utility/global_const';
import { SharedModule } from '../../../../shared/shared.module';
import { SearchOptions } from '../../../../_models/common.model';
import { MatDialog } from '@angular/material/dialog';
import { PlotCreationModalComponent } from '../plot-creation-modal/plot-creation-modal.component';

@Component({
  selector: 'app-visual-data-header',
  imports: [MatIconModule, MatButtonModule, SharedModule],
  templateUrl: './visual-data-header.component.html',
  styleUrl: './visual-data-header.component.css',
})
export class VisualDataHeaderComponent {
  visualDataStore = inject(visualDataStore);
  dialog = inject(MatDialog);
  g_const = g_const;

  searchFilters(searchOptions: SearchOptions) {
    this.visualDataStore.loadUserPlotData({ cursor: 0, searchOptions });
  }

  toggleShowFavouritePlots() {
    this.visualDataStore.toggleFavouritePlots();
  }

  openPlotCreationModal() {
    this.dialog.open(PlotCreationModalComponent, {
      minWidth: '70vw',
      minHeight: '30vh',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
    });
  }
}
