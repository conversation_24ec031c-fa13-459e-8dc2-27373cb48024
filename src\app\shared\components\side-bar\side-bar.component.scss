@use '@angular/material' as mat;

.mat-drawer-container {
  box-shadow: 19px 0px 50px -3px rgba(0, 0, 0, 0.1);
  width: 80px;
}

.hamburger-btn {
  position: absolute;
  left: 90px;
}

/* Tooltip styling */
::ng-deep .sidebar-tooltip {
  max-width: 100px;
  text-align: center;
  white-space: pre-line;
}

::ng-deep .mdc-tooltip__surface {
  background-color: var(--mat-sys-surface-container) !important;
  color: var(--mat-sys-on-surface) !important;
}

.sidebar-container {
  background-color: var(--mat-sys-surface-container);
}

.sidebar-divider {
  border-color: var(--mat-sys-outline-variant);
}

.sidebar-icon {
  color: var(--mat-sys-on-surface-variant);

  &.active {
    color: var(--mat-sys-on-primary);
  }
}
