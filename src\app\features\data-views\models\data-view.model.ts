import { NumOperations } from '../../../_models/data-view/numericOperations.enum';
import { Data, Image } from 'plotly.js';
import {
  BackendResponse,
  PaginationInfo,
} from '../../../_models/visual-data/visual-data.model';

//TODO comes from BE
export interface DataViewBackendResponse<T> {
  data: T;
  status: string;
  message: string;
  errors: string | null;
  pagination: DataViewPaginationInfo | null;
}

//TODO comes from BE
export interface DataViewPaginationInfo {
  total_records: number;
  total_pages: number;
  current_page: number;
  page_size: number;
}

export interface MathOperationRequest {
  operations: opParams;
}

//TODO comes from BE
export interface DynamicAddColumn {
  col1: string;
  operation: string;
  col2_or_scalar: string;
  metric_name: string;
  reference_column?: string;
  position?: string;
}

export interface FilterDataRow {
  logicOperator: string;
  selectedColumn: string;
  selectedOperator: string;
  isBetweenOperator: boolean;
  columnType?: string;
  availableOperators?: string[];
  availableValues?: string[];
  selectedValue: string | null;
  minValue?: string | null;
  maxValue?: string | null;
  singleValue?: string | null;
  minPlaceholder?: string;
  maxPlaceholder?: string;
  showValues?: boolean;
}

//TODO GET dataview/column-choices/${file_id}
export interface ColumnChoices {
  message: string;
  data: {
    Categories: string[];
    Numerical: string[];
    TimeSpan: string[];
    DateTime: string[];
    Date: string[];
    Boolean: string[];
    Text: string[];
  };
}

//TODO GET dataview/column-values/${file_id}
export interface ColumnValuesData {
  values: ColumnValues;
  operators: FilterOperators;
}

//TODO comes from BE
export interface FilterOperators {
  [key: string]: Record<string, string>;

  Numerical: Record<string, string>;
  Categories: Record<string, string>;
  'Date and Time': Record<string, string>;
  Boolean: Record<string, string>;
  Text: Record<string, string>;
}

//TODO comes from BE
export interface ColumnValues {
  Categories: CategoryValues[];
  Numerical: Record<string, NumericalValues>;
  TimeSpan: unknown;
  DateTime: unknown;
  Date: unknown;
  Boolean: unknown;
  Text: unknown;
}

//TODO comes from BE
export interface CategoryValues {
  name: string;
  options: string[];
  datatype: string;
}

//TODO comes from BE
export interface NumericalValues {
  column: string;
  min: number;
  max: number;
  datatype: string;
}

//TODO POST dataview/data/statistics/${file_id}/
export type StatisticsData = Record<string, StatisticsValue>;

export interface StatisticsValue {
  count: number;
  mean: number;
  std: number;
  min: number;
  '25%': number;
  '50%': number;
  '75%': number;
  max: number;
  'uniqueness %': number;
  'Zero value %': number;
}

//TODO GET dataview/display/file/${file_id}/
export interface FileData {
  data: Record<string, string | number>;
  //TODO check if obsolete
  json_data?: {
    layout: {
      images: Image[];
    };
  };
  columns: string[];
  metadata: FileMetaData;
}

export interface FileMetaData {
  filename: string;
  num_columns: number;
  num_rows: number;
  file_size: string;
  column_names: string[];
  filter_active: boolean;
  filter_obj_id: number;
  resolution: string;
}

//---------------

//TODO POST files/files/process/batch/`
export interface BatchUploadResult {
  successfully_processed: SuccessfullyProcessed[];
  failed_to_process: unknown[];
}

export interface SuccessfullyProcessed {
  file_id: number;
  file_name: string;
  processed_file_id: number;
}

//TODO GET dataview/filter/file/${filterId}/
export interface FilterData {
  user: number;
  file: number;
  filter_purpose_type: string;
  filter_operations: FilterOperation[];
  sort_order: string;
  sort_column: unknown;
  created_at: string;
  updated_at: string;
}

export interface FilterOperation {
  filter_column: string;
  filter_operator: string;
  filter_value: string;
  logic: string;
}

//TODO POST dataview/filter/data/${file_id}/
export interface FilterColumnData {
  filter_obj_id: number;
  result_data: Record<string, string | number>[];
}

//TODO POST files/files/process/${file_id}/
export interface FileUploadProgress {
  json_data: BackendResponse<Record<string, string | number>>;
  data: {
    task_id: string;
    file_name?: string;
  };
  file_id: number;
  file_name: string;
  columns: string[];
  processed_file_id: number;
  message?: string;
}

//TODO POST files/files/upload-status/${folder_id}/
export interface FileUploadStatus {
  id: number;
}

//TODO POST files/files/upload-url/${file_id}/
export interface FileUploadUrl {
  signed_url: string;
  file_key: string;
}

//TODO GET files/ColumnInfo/${file_id}/
export interface ColumnInfo {
  id: number;
  file_name: string;
  file_metadata: FileMetadata;
  columns: Column[];
}

export interface Column {
  name: string;
  type: string;
  color: string;
  options: string[];
}

//TODO POST dataview/set-file-metdadata/${file_id}/
export interface FileMetadata {
  delimiter: string;
  data_type_rows: number;
  header: boolean;
  header_row: number;
  json_data?: { data: Data[] };
  columns: string[];
}

//TODO GET appoptions/DropDown/datatypes/
export interface ColumnDataType {
  id: number;
  name: string;
  description: string;
  options: Options;
}

export interface Options {
  currencyCodes: Record<string, string>;
  currencyLocales: Record<string, string>;
}

//------------------

export interface FolderStructure {
  folder_structure: FolderStructureDetail;
}

export interface FolderStructureDetail {
  files: number[];
  folder_id: number;
  subfolders: unknown[];
}

//TODO POST files/files/upload-folder/${folder_id}/status/
export interface FolderStatus {
  successful_files: SuccessfulFile[];
  successful_folders: SuccessfulFolder[];
  status: string;
  upload_statistics: UploadStatistics;
  processing_files: number[];
  further_processing_files: number[];
}

export interface SuccessfulFile {
  file_id: number;
  file_name: string;
  file_path: string;
  file_path_names: string;
  message: string;
  further_processing: boolean;
}

export interface SuccessfulFolder {
  folder_id: number;
  folder_path: string;
  folder_name: string;
  folder_path_names: string;
  message: string;
}

export interface UploadStatistics {
  num_folders_uploaded: number;
  num_files_uploaded: number;
  message: string;
}

//TODO PUT dataview/file/${file_id}/rename/
export interface RenameData {
  new_name: string;
}

//-----------------

export interface UploadFolder {
  successful_files: SuccessfulFile[];
  successful_folders: SuccessfulFolder[];
  status: string;
  upload_statistics: UploadStatistics;
  processing_files: number[];
  further_processing_files: number[];
}

export interface SuccessfulFile {
  file_id: number;
  file_name: string;
  file_path: string;
  file_path_names: string;
  message: string;
  further_processing: boolean;
}

export interface SuccessfulFolder {
  folder_id: number;
  folder_path: string;
  folder_name: string;
  folder_path_names: string;
  message: string;
}

export interface UploadStatistics {
  num_folders_uploaded: number;
  num_files_uploaded: number;
  message: string;
}

//----------------

export interface opParams {
  metric_name: string;
  col1: string;
  operation: NumOperations;
  col2_or_scalar: string;
}

export interface DataViewSearch {
  filename: string;
}

export interface PrimaryColumnData {
  'Possible ID_Columns': string[];
  Alternatives: string[];
}

export interface Statistic {
  count: number;
  mean: number;
  std: number;
  min: number;
  '25%': number;
  '50%': number;
  '75%': number;
  max: number;
  'uniqueness %': number;
  'Zero value %': number;
}

//TODO POST dataview/set-column-ID/${file_id}/
export interface ColumnIDSetData {
  id_column: string;
  file_path: boolean;
  columns?: string;
  file_name?: string;
}

//TODO POST dataview/math-advanced/${file_id}/
export interface ColumnAddData {
  rows: Record<string, string | number>[];
  coloured_columns: string[];
}

//TODO POST dataview/image/statistics/${image_file_id}/
export type DynamicStatistics = Record<string, Statistic>;

export interface FolderData {
  folder_id: string;
  folder_name: string;
  files: FileData[];
  subfolders: FolderData[];
}

export interface FileData {
  id: number;
  name: string;
  size: string;
  type: string;
}

export interface DataViewResponse {
  status: string;
  message: string;
  data: FolderData[];
}

export interface ProcessFileStatusInterface {
  status: string;
  message: string;
  data: {
    status: string;
  };
  errors: {
    error: string;
  };
  pagination: PaginationInfo;
}

export interface DirectoryEntry {
  id: number;
  type: 'directory';
  name: string;
  children: (DirectoryEntry | FileEntry)[];
}

export interface FileEntry {
  id: number;
  type: 'file';
  name: string;
  path: string;
}
export interface FormInfo {
  id: number;
  name: string;
  description: string;
}

export interface StorageAddonInterface {
  message: string;
  storage: {
    total_allowed: string;
    total_available: string;
    total_used: string;
  };
}
