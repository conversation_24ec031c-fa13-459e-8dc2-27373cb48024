import { Component, Input, OnChanges } from '@angular/core';

interface PreviewItem {
  label: string;
  value: string | number;
}

@Component({
  selector: 'app-training-preview',
  imports: [],
  templateUrl: './training-preview.component.html',
  styleUrl: './training-preview.component.css',
})
export class TrainingPreviewComponent implements OnChanges {
  @Input() trainingDetails: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  previewItems: PreviewItem[] = [];

  ngOnChanges(): void {
    if (this.trainingDetails) {
      this.previewItems = [
        { label: 'Name', value: this.trainingDetails.name || '' },
        {
          label: 'Training Data',
          value: this.trainingDetails.trainingData || '',
        },
        {
          label: 'Analysis Goal Name',
          value: this.trainingDetails.analysisGoalName || '',
        },
        {
          label: 'Analysis Goal Type',
          value: this.trainingDetails.analysisGoalType || '',
        },
        {
          label: 'Selected Target',
          value: this.trainingDetails.selectedTarget || '',
        },
        {
          label: 'Selected Features',
          value: this.trainingDetails.selectedFeatures || '',
        },
        {
          label: 'Train Size',
          value: `${this.trainingDetails.train_size || 0}%`,
        },
        {
          label: 'Test Size',
          value: `${this.trainingDetails.test_size || 0}%`,
        },
      ];
    }
  }
}
