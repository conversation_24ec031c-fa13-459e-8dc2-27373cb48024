@if (this.loading) {
  <div class="flex justify-center items-center p-5">
    <mat-spinner></mat-spinner>
  </div>
} @else {
  <div class="plot-creation-model-container h-full w-full bg-[#F1F3F9]">
    <div class="overflow-y-auto">
      <div class="p-2">
        <h2 mat-dialog-title>New Plot</h2>
      </div>
      <hr />
      <div mat-dialog-content>
        <form [formGroup]="plotCreationForm" class="p-2">
          <!-- PLOT TYPE SELECTION -->
          <div class="grid grid-cols-[70%_30%] w-full pb-6">
            <div>
              <h3 class="pb-2">Plot Type</h3>
              <mat-form-field class="w-full">
                <mat-select
                  formControlName="plotType"
                  (selectionChange)="onPlotTypeSelectionChange($event)"
                  [placeholder]="'Select Plot Type'">
                  <mat-select-trigger>
                    @if (
                      this.plotCreationForm.value !== null &&
                      this.plotCreationForm.value.plotType !== null
                    ) {
                      @if (this.plotCreationForm.value.plotType?.icon) {
                        <img
                          class="mr-2 inline"
                          src="assets/charts/{{
                            this.plotCreationForm.value.plotType?.icon
                          }}.svg"
                          alt="Chart Type Icon" />
                      }
                      <span>{{
                        this.plotCreationForm.value.plotType?.plot_name
                      }}</span>
                    }
                  </mat-select-trigger>

                  @for (group of plotTypesArray(); track group.key) {
                    <mat-optgroup [label]="group.key">
                      @for (plot of group.values; track plot.plot_name) {
                        <mat-option [value]="plot">
                          @if (plot.icon) {
                            <img
                              height="15"
                              width="15"
                              class="mr-2 inline"
                              src="assets/charts/{{ plot.icon }}.svg"
                              alt="Chart Type Icon" />
                          }
                          {{ plot.plot_name }}
                        </mat-option>
                      }
                    </mat-optgroup>
                  }
                </mat-select>
              </mat-form-field>
            </div>
            <!-- Switchs Setting buttons-->
            <div class="flex justify-center items-center">
              @for (
                switchSettings of this.availableSwitchSettingsArray();
                track switchSettings
              ) {
                <!-- NOTE : Below button has styled using css, reason : Can't use tailwind directly here, need CSS to override material styling -->
                <mat-button-toggle-group
                  class="plot-create-toggle-group"
                  hideSingleSelectionIndicator="true"
                  [value]="switchSettings.toggleOptions[0]">
                  @for (
                    toggleOpt of switchSettings.toggleOptions;
                    track toggleOpt
                  ) {
                    <mat-button-toggle
                      [value]="toggleOpt"
                      (change)="
                        onSwitchSettingSelectionChange(
                          $event,
                          switchSettings.setting_name
                        )
                      ">
                      <div>{{ toggleOpt }}</div>
                    </mat-button-toggle>
                  }
                </mat-button-toggle-group>
              }
            </div>
          </div>

          <!-- FILE SELECTION -->
          <div class="pb-6">
            <h3 class="pb-2">Data Source</h3>
            <app-data-source-input
              (selectionEmitted)="handleFileSelectionChange($event)">
            </app-data-source-input>
          </div>

          <!-- ONLY SHOW Option set and settings when plot type and file is selected -->
          @if (
            this.plotCreationForm.controls.plotType.valid &&
            this.plotCreationForm.controls.selectedFileInfo.valid
          ) {
            <!-- Option set -->
            <div
              formArrayName="selectedOptions"
              class="flex flex-col gap-6 pb-6">
              <!-- <h3>Options Configuration</h3> -->
              @for (
                optSet of getSelectedOptionSetArray().controls;
                let optIndex = $index;
                track optSet
              ) {
                <div [formGroupName]="optIndex">
                  <h3 class="pb-2">{{ optSet.get('optionName')?.value }}</h3>
                  <!-- Smart-bucking :  Option set has only one Smart bucketing strategy hence it is not under optionSet.values but property on optionSet.selectedSmartBucket-->
                  @if (optSet.get('canSmartBucketed')?.value === true) {
                    <div class="mb-2">
                      <mat-form-field appearance="fill">
                        <mat-select
                          [formControlName]="'selectedSmartBucket'"
                          [placeholder]="'Select Smart Bucketing'">
                          @for (
                            option of availableSmartBucketChoices;
                            track option.smart_bucketing_value
                          ) {
                            <mat-option [value]="option">
                              {{ option.smart_bucketing_name }}
                            </mat-option>
                          }
                        </mat-select>
                      </mat-form-field>
                    </div>
                  }
                  <div formArrayName="values" class="flex flex-col gap-2">
                    @for (
                      val of getValuesArrayFromOptionSet(optSet).controls;
                      let valIndex = $index;
                      track val
                    ) {
                      <!-- row container -->
                      <div
                        [formGroupName]="valIndex"
                        class="flex items-center gap-2">
                        <mat-form-field appearance="fill" class="flex">
                          <mat-select
                            [formControlName]="'selectedColName'"
                            [placeholder]="'Select Column'">
                            @for (
                              availableColChoice of this.getColumnNameFromOptionSetBasedOnOptSetFormControl(
                                optSet
                              );
                              track availableColChoice
                            ) {
                              <mat-option [value]="availableColChoice">
                                {{ availableColChoice }}
                              </mat-option>
                            }
                          </mat-select>
                        </mat-form-field>
                        <!-- Aggregation Set -->
                        @if (optSet.get('aggregations')?.value) {
                          <mat-form-field appearance="fill">
                            <mat-select
                              [formControlName]="'selectedArggregation'"
                              [placeholder]="'Select Aggregation'">
                              @for (
                                option of availableAggregationChoices;
                                track option
                              ) {
                                <mat-option [value]="option">
                                  {{ option }}
                                </mat-option>
                              }
                            </mat-select>
                          </mat-form-field>
                        }
                        <!-- Conditionally show Add or Remove button -->
                        @if (optSet.get('multiple')?.value === true) {
                          @if (valIndex === 0) {
                            <button
                              mat-stroked-button
                              color="primary"
                              type="button"
                              (click)="addOptionValueInOptionSet(optSet)"
                              class="flex justify-center items-center">
                              <mat-icon>add</mat-icon>
                              <div>Add Column</div>
                            </button>
                          } @else {
                            <button
                              mat-stroked-button
                              type="button"
                              (click)="
                                removeOptionValueFromOptionSet(optSet, valIndex)
                              "
                              class="flex justify-center items-center text-red-600 border-red-600 hover:bg-red-50 hover:border-red-700">
                              <mat-icon>close</mat-icon>
                              <div class="ml-2">Remove</div>
                            </button>
                          }
                        }
                      </div>
                    }
                  </div>
                </div>
              }
            </div>

            <!-- Settings -->
            <div formArrayName="selectedSettings">
              <h3 class="pb-2">Settings</h3>
              <div>
                <!-- Dropdown Settings -->
                <mat-form-field>
                  <!-- Below  [multiple] cant be change dyanmically once initialize-->
                  <mat-select
                    [multiple]="true"
                    [placeholder]="'Select Settings'">
                    @for (
                      dropDownSetting of this.availableDropdownSettingsArray();
                      track dropDownSetting
                    ) {
                      <mat-option
                        [value]="dropDownSetting"
                        (onSelectionChange)="
                          onDropDownSettingSelectionChange($event)
                        ">
                        {{ dropDownSetting }}
                      </mat-option>
                    }
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
          }
        </form>
      </div>

      <hr />
      <div mat-dialog-actions class="flex justify-start">
        <button
          mat-flat-button
          color="primary"
          (click)="onSubmit()"
          type="submit">
          Save
        </button>

        <button
          mat-stroked-button
          color="primary"
          (click)="closePlotCreationModal()"
          type="button">
          Cancel
        </button>
      </div>
    </div>
  </div>
}
