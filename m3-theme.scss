@use 'sass:map';
@use '@angular/material' as mat;

// ========================================
// FIGMA-ALIGNED COLOR PALETTES
// Using exact colors from Figma design tokens
// ========================================

$_palettes: (
  primary: (
    0: #000000,
    10: #001d36,        // on-primary-container (light)
    20: #003259,        // on-primary (dark)
    25: #003d6b,
    30: #00497d,
    35: #18558a,
    40: #296197,        // primary (light) - FIGMA EXACT
    50: #467ab2,
    60: #6194cd,
    70: #7cafea,
    80: #a1cafd,        // primary (dark) - FIGMA EXACT
    90: #d2e4ff,        // primary-container (light) - FIGMA EXACT
    95: #eaf1ff,
    98: #f8f9ff,        // background (light) - FIGMA EXACT
    99: #fdfcff,
    100: #ffffff,       // on-primary (light) - FIGMA EXACT
  ),
  secondary: (
    0: #000000,
    10: #001f24,        // on-secondary-container (light) - FIGMA EXACT
    20: #00363d,        // on-secondary (dark) - FIGMA EXACT
    25: #00424a,
    30: #004f58,        // secondary-container (dark) - FIGMA EXACT
    35: #005b66,
    40: #006874,        // secondary (light) - FIGMA EXACT
    50: #2f828e,
    60: #4d9ca8,
    70: #6ab7c4,
    80: #82d3e0,        // secondary (dark) - FIGMA EXACT
    90: #9eeffd,        // secondary-container (light) - FIGMA EXACT
    95: #d1f8ff,
    98: #edfcff,
    99: #f6feff,
    100: #ffffff,       // on-secondary (light) - FIGMA EXACT
  ),
  tertiary: (
    0: #000000,
    10: #220f46,
    20: #38265d,
    25: #433169,
    30: #4f3d75,
    35: #5b4881,
    40: #67548e,
    50: #816da9,
    60: #9b86c4,
    70: #b6a1e1,
    80: #d2bcfe,
    90: #eaddff,
    95: #f6edff,
    98: #fef7ff,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,         // shadow/scrim - FIGMA EXACT
    4: #0b0e12,         // surface-container-lowest (dark) - FIGMA EXACT
    6: #101418,         // surface (dark) - FIGMA EXACT
    10: #191c20,        // on-background (light) - FIGMA EXACT
    12: #181c20,        // surface-container-low (dark) - FIGMA EXACT
    17: #1c2024,        // surface-container (dark) - FIGMA EXACT
    20: #272a2f,        // surface-container-high (dark) - FIGMA EXACT
    22: #323539,        // surface-container-highest (dark) - FIGMA EXACT
    24: #36393e,        // surface-bright (dark) - FIGMA EXACT
    25: #393b40,
    30: #43474e,        // on-surface-variant (light) - FIGMA EXACT
    35: #505257,
    40: #5c5e63,
    50: #73777f,        // outline (light) - FIGMA EXACT
    60: #8d9199,        // outline (dark) - FIGMA EXACT
    70: #a9abb1,
    80: #c3c6cf,        // on-surface-variant (dark) - FIGMA EXACT
    87: #c3c6cf,        // outline-variant (light) - FIGMA EXACT
    90: #e0e2e8,        // on-surface (dark) - FIGMA EXACT
    92: #e0e2e8,        // surface-container-highest (light) - FIGMA EXACT
    94: #e6e8ee,        // surface-container-high (light) - FIGMA EXACT
    95: #eceef4,        // surface-container (light) - FIGMA EXACT
    96: #f1f3f9,        // surface-container-low (light) - FIGMA EXACT
    98: #f7f9ff,        // surface (light) - FIGMA EXACT
    99: #fafbff,        // surface-light (light) - FIGMA EXACT
    100: #ffffff,       // surface-container-lowest (light) - FIGMA EXACT
  ),
  neutral-variant: (
    0: #000000,
    10: #161c23,
    20: #2b3139,
    25: #363c44,
    30: #42474f,
    35: #4d535b,
    40: #595f67,
    50: #727780,
    60: #8c919a,
    70: #a6abb5,
    80: #c2c7d1,
    90: #dee3ed,
    95: #ecf1fb,
    98: #f8f9ff,
    99: #fdfcff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes, neutral-variant),
  error: map.get($_palettes, error),
);

$_primary: map.merge(map.get($_palettes, primary), $_rest);
$_secondary: map.merge(map.get($_palettes, secondary), $_rest);
$_tertiary: map.merge(map.get($_palettes, tertiary), $_rest);

// ========================================
// THEME DEFINITIONS USING FIGMA COLORS
// ========================================

$light-theme: mat.define-theme(
  (
    color: (
      theme-type: light,
      primary: $_primary,
      secondary: $_secondary,
      tertiary: $_tertiary,
    ),
    typography: (
      brand-family: 'Roboto',
    ),
  )
);

$dark-theme: mat.define-theme(
  (
    color: (
      theme-type: dark,
      primary: $_primary,
      secondary: $_secondary,
      tertiary: $_tertiary,
    ),
    typography: (
      brand-family: 'Roboto',
    ),
  )
);
