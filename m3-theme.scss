@use 'sass:map';
@use '@angular/material' as mat;

// ========================================
// SINGLE SOURCE OF TRUTH FOR FIGMA COLORS
// All color tokens integrated from Figma design system
// ========================================

// 🔄 SYNC VERIFICATION CHECKLIST:
// ✅ Primary colors: #296197 (light), #a1cafd (dark)
// ✅ Surface colors: #f7f9ff (light), #101418 (dark)
// ✅ Container colors: #eceef4 (light), #1c2024 (dark)
// ✅ Text colors: #181c20 (light), #e0e2e8 (dark)
//
// 🚨 IMPORTANT: When updating colors, modify BOTH files:
// 1. This file (m3-theme.scss) - SCSS palettes for Angular Material
// 2. src/styles/_color.scss - CSS custom properties for custom components

// ========================================
// FIGMA-ALIGNED COLOR PALETTES
// SYNCHRONIZED with _color.scss - DO NOT MODIFY WITHOUT UPDATING BOTH FILES
// ========================================

// SYNC VERIFICATION: These colors MUST match _color.scss exactly
// Light mode: .light-mode { --md-sys-color-* }
// Dark mode: .dark-mode { --md-sys-color-* }

$_palettes: (
  primary: (
    0: #000000,
    10: #001d36,        // SYNC: --md-sys-color-on-primary-container (light) = #001d36
    20: #003259,        // SYNC: --md-sys-color-on-primary (dark) = #003259
    25: #003d6b,
    30: #00497d,
    35: #18558a,
    40: #296197,        // SYNC: --md-sys-color-primary (light) = #296197 ✓
    50: #467ab2,
    60: #6194cd,
    70: #7cafea,
    75: #1a4975,        // SYNC: --md-sys-color-primary-container (dark) = #1a4975
    80: #a1cafd,        // SYNC: --md-sys-color-primary (dark) = #a1cafd ✓
    90: #d2e4ff,        // SYNC: --md-sys-color-primary-container (light) = #d2e4ff ✓
    95: #eaf1ff,
    98: #f8f9ff,        // SYNC: --md-sys-color-background (light) = #f8f9ff ✓
    99: #fdfcff,
    100: #ffffff,       // SYNC: --md-sys-color-on-primary (light) = #ffffff ✓
  ),
  secondary: (
    0: #000000,
    10: #001f24,        // SYNC: --md-sys-color-on-secondary-container (light) = #001f24 ✓
    20: #00363d,        // SYNC: --md-sys-color-on-secondary (dark) = #00363d ✓
    25: #00424a,
    30: #004f58,        // SYNC: --md-sys-color-secondary-container (dark) = #004f58 ✓
    35: #005b66,
    40: #006874,        // SYNC: --md-sys-color-secondary (light) = #006874 ✓
    50: #2f828e,
    60: #4d9ca8,
    70: #6ab7c4,
    80: #82d3e0,        // SYNC: --md-sys-color-secondary (dark) = #82d3e0 ✓
    90: #9eeffd,        // SYNC: --md-sys-color-secondary-container (light) = #9eeffd ✓
    95: #d1f8ff,
    98: #edfcff,
    99: #f6feff,
    100: #ffffff,       // SYNC: --md-sys-color-on-secondary (light) = #ffffff ✓
  ),
  tertiary: (
    0: #000000,
    10: #220f46,        // SYNC: --md-sys-color-on-tertiary-container (light) = #220f46 ✓
    20: #38265c,        // SYNC: --md-sys-color-on-tertiary (dark) = #38265c ✓
    25: #433169,
    30: #4f3d74,        // SYNC: --md-sys-color-tertiary-container (dark) = #4f3d74 ✓
    35: #5b4881,
    40: #67548e,        // SYNC: --md-sys-color-tertiary (light) = #67548e ✓
    50: #816da9,
    60: #9b86c4,
    70: #b6a1e1,
    80: #d2bcfd,        // SYNC: --md-sys-color-tertiary (dark) = #d2bcfd ✓
    90: #eaddff,        // SYNC: --md-sys-color-tertiary-container (light) = #eaddff ✓
    95: #f6edff,
    98: #fef7ff,
    99: #fffbff,
    100: #ffffff,       // SYNC: --md-sys-color-on-tertiary (light) = #ffffff ✓
  ),
  neutral: (
    0: #000000,         // SYNC: --md-sys-color-shadow/scrim = #000000 ✓
    4: #0b0e12,         // SYNC: --md-sys-color-surface-container-lowest (dark) = #0b0e12 ✓
    6: #101418,         // SYNC: --md-sys-color-surface (dark) = #101418 ✓
    10: #191c20,        // SYNC: --md-sys-color-on-background (light) = #191c20 ✓
    12: #181c20,        // SYNC: --md-sys-color-surface-container-low (dark) = #181c20 ✓
    17: #1c2024,        // SYNC: --md-sys-color-surface-container (dark) = #1c2024 ✓
    20: #272a2f,        // SYNC: --md-sys-color-surface-container-high (dark) = #272a2f ✓
    22: #323539,        // SYNC: --md-sys-color-surface-container-highest (dark) = #323539 ✓
    23: #181c20,        // SYNC: --md-sys-color-on-surface (light) = #181c20 ✓
    24: #36393e,        // SYNC: --md-sys-color-surface-bright (dark) = #36393e ✓
    25: #393b40,
    30: #43474e,        // SYNC: --md-sys-color-on-surface-variant (light) = #43474e ✓
    35: #505257,
    40: #5c5e63,
    50: #73777f,        // SYNC: --md-sys-color-outline (light) = #73777f ✓
    60: #8d9199,        // SYNC: --md-sys-color-outline (dark) = #8d9199 ✓
    70: #a9abb1,
    80: #c3c6cf,        // SYNC: --md-sys-color-on-surface-variant (dark) = #c3c6cf ✓
    87: #c3c6cf,        // SYNC: --md-sys-color-outline-variant (light) = #c3c6cf ✓
    90: #e0e2e8,        // SYNC: --md-sys-color-on-surface (dark) = #e0e2e8 ✓
    92: #e0e2e8,        // SYNC: --md-sys-color-surface-container-highest (light) = #e0e2e8 ✓
    94: #e6e8ee,        // SYNC: --md-sys-color-surface-container-high (light) = #e6e8ee ✓
    95: #eceef4,        // SYNC: --md-sys-color-surface-container (light) = #eceef4 ✓
    96: #f1f3f9,        // SYNC: --md-sys-color-surface-container-low (light) = #f1f3f9 ✓
    98: #f7f9ff,        // SYNC: --md-sys-color-surface (light) = #f7f9ff ✓
    99: #fafbff,        // SYNC: --md-sys-color-surface-light (light) = #fafbff ✓
    100: #ffffff,       // SYNC: --md-sys-color-surface-container-lowest (light) = #ffffff ✓
  ),
  neutral-variant: (
    0: #000000,
    10: #161c23,
    20: #2b3139,
    25: #363c44,
    30: #42474f,
    35: #4d535b,
    40: #595f67,
    50: #727780,
    60: #8c919a,
    70: #a6abb5,
    80: #c2c7d1,
    90: #dee3ed,
    95: #ecf1fb,
    98: #f8f9ff,
    99: #fdfcff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes, neutral-variant),
  error: map.get($_palettes, error),
);

$_primary: map.merge(map.get($_palettes, primary), $_rest);
$_secondary: map.merge(map.get($_palettes, secondary), $_rest);
$_tertiary: map.merge(map.get($_palettes, tertiary), $_rest);

// ========================================
// THEME DEFINITIONS USING FIGMA COLORS
// ========================================

$light-theme: mat.define-theme(
  (
    color: (
      theme-type: light,
      primary: $_primary,
      tertiary: $_tertiary,
    ),
    typography: (
      brand-family: 'Roboto',
    ),
  )
);

$dark-theme: mat.define-theme(
  (
    color: (
      theme-type: dark,
      primary: $_primary,
      tertiary: $_tertiary,
    ),
    typography: (
      brand-family: 'Roboto',
    ),
  )
);


