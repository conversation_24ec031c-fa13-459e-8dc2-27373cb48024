@use 'sass:map';
@use '@angular/material' as mat;

$_palettes: (
  primary: (
    0: #000000,
    10: #001d36,
    20: #003259,
    25: #002341,
    30: #154571,
    35: #1a4975,
    40: #296197,
    50: #467ab2,
    60: #6194cd,
    70: #7cafea,
    80: #a1cafd,
    90: #d2e4ff,
    95: #eaf1ff,
    98: #f8f9ff,
    99: #fdfcff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #001f24,
    20: #00363d,
    25: #00272c,
    30: #004f58,
    35: #004a53,
    40: #006874,
    50: #2f828e,
    60: #4d9ca8,
    70: #6ab7c4,
    80: #82d3e0,
    90: #9eeffd,
    95: #d1f8ff,
    98: #edfcff,
    99: #f6feff,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #220f46,
    20: #38265c,
    25: #29174d,
    30: #4f3d74,
    35: #4b3970,
    40: #67548e,
    50: #816da9,
    60: #9b86c4,
    70: #b6a1e1,
    80: #d2bcfd,
    90: #eaddff,
    95: #f6edff,
    98: #fef7ff,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    4: #0b0e12,
    6: #101418,
    10: #181c20,
    12: #1c2024,
    17: #272a2f,
    20: #2d3135,
    22: #323539,
    24: #36393e,
    25: #393b40,
    30: #43474e,
    35: #505257,
    40: #5c5e63,
    50: #73777f,
    60: #8d9199,
    70: #a9abb1,
    80: #c3c6cf,
    87: #d8dae0,
    90: #e0e2e8,
    92: #e6e8ee,
    94: #eceef4,
    95: #eff1f6,
    96: #f1f3f9,
    98: #f7f9ff,
    99: #fdfcff,
    100: #ffffff,
  ),
  neutral-variant: (
    0: #000000,
    10: #20242b,
    20: #3f434a,
    25: #363c44,
    30: #43474e,
    35: #4d535b,
    40: #43474e,
    50: #73777f,
    60: #8d9199,
    70: #a6abb5,
    80: #c3c6cf,
    90: #dfe2eb,
    95: #ecf1fb,
    98: #f8f9ff,
    99: #fdfcff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #3b080f,
    20: #561d22,
    25: #440f15,
    30: #733337,
    35: #6e2f33,
    40: #8f4a4e,
    50: #aa5f63,
    60: #c8797d,
    70: #e69499,
    80: #ffb3b5,
    90: #ffdada,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes, neutral-variant),
  error: map.get($_palettes, error),
);

$_primary: map.merge(map.get($_palettes, primary), $_rest);
$_secondary: map.merge(map.get($_palettes, secondary), $_rest);
$_tertiary: map.merge(map.get($_palettes, tertiary), $_rest);

$light-theme: mat.define-theme(
  (
    color: (
      theme-type: light,
      primary: $_primary,
      tertiary: $_tertiary,
    ),
    typography: (
      brand-family: 'Roboto',
    ),
  )
);

$dark-theme: mat.define-theme(
  (
    color: (
      theme-type: dark,
      primary: $_primary,
      tertiary: $_tertiary,
    ),
    typography: (
      brand-family: 'Roboto',
    ),
  )
);
