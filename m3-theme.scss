@use 'sass:map';
@use '@angular/material' as mat;

// ========================================
// SINGLE SOURCE OF TRUTH FOR FIGMA COLORS
// All color tokens integrated from Figma design system
// ========================================

// ========================================
// FIGMA-ALIGNED COLOR PALETTES
// Using exact colors from Figma design tokens in _color.scss
// ========================================

// Extract colors from CSS custom properties defined in _color.scss
// These match exactly with the Figma design tokens

$_palettes: (
  primary: (
    0: #000000,
    10: #001d36,        // --md-sys-color-on-primary-container (light)
    20: #003259,        // --md-sys-color-on-primary (dark)
    25: #003d6b,
    30: #00497d,
    35: #18558a,
    40: #296197,        // --md-sys-color-primary (light) - FIGMA EXACT
    50: #467ab2,
    60: #6194cd,
    70: #7cafea,
    80: #a1cafd,        // --md-sys-color-primary (dark) - FIGMA EXACT
    90: #d2e4ff,        // --md-sys-color-primary-container (light) - FIGMA EXACT
    95: #eaf1ff,
    98: #f8f9ff,        // --md-sys-color-background (light) - FIGMA EXACT
    99: #fdfcff,
    100: #ffffff,       // --md-sys-color-on-primary (light) - FIGMA EXACT
  ),
  secondary: (
    0: #000000,
    10: #001f24,        // --md-sys-color-on-secondary-container (light) - FIGMA EXACT
    20: #00363d,        // --md-sys-color-on-secondary (dark) - FIGMA EXACT
    25: #00424a,
    30: #004f58,        // --md-sys-color-secondary-container (dark) - FIGMA EXACT
    35: #005b66,
    40: #006874,        // --md-sys-color-secondary (light) - FIGMA EXACT
    50: #2f828e,
    60: #4d9ca8,
    70: #6ab7c4,
    80: #82d3e0,        // --md-sys-color-secondary (dark) - FIGMA EXACT
    90: #9eeffd,        // --md-sys-color-secondary-container (light) - FIGMA EXACT
    95: #d1f8ff,
    98: #edfcff,
    99: #f6feff,
    100: #ffffff,       // --md-sys-color-on-secondary (light) - FIGMA EXACT
  ),
  tertiary: (
    0: #000000,
    10: #220f46,        // --md-sys-color-on-tertiary-container
    20: #38265d,        // --md-sys-color-on-tertiary (dark)
    25: #433169,
    30: #4f3d75,        // --md-sys-color-tertiary-container (dark)
    35: #5b4881,
    40: #67548e,        // --md-sys-color-tertiary (light)
    50: #816da9,
    60: #9b86c4,
    70: #b6a1e1,
    80: #d2bcfe,        // --md-sys-color-tertiary (dark)
    90: #eaddff,        // --md-sys-color-tertiary-container (light)
    95: #f6edff,
    98: #fef7ff,
    99: #fffbff,
    100: #ffffff,       // --md-sys-color-on-tertiary (light)
  ),
  neutral: (
    0: #000000,         // --md-sys-color-shadow/scrim - FIGMA EXACT
    4: #0b0e12,         // --md-sys-color-surface-container-lowest (dark) - FIGMA EXACT
    6: #101418,         // --md-sys-color-surface (dark) - FIGMA EXACT
    10: #191c20,        // --md-sys-color-on-background (light) - FIGMA EXACT
    12: #181c20,        // --md-sys-color-surface-container-low (dark) - FIGMA EXACT
    17: #1c2024,        // --md-sys-color-surface-container (dark) - FIGMA EXACT
    20: #272a2f,        // --md-sys-color-surface-container-high (dark) - FIGMA EXACT
    22: #323539,        // --md-sys-color-surface-container-highest (dark) - FIGMA EXACT
    24: #36393e,        // --md-sys-color-surface-bright (dark) - FIGMA EXACT
    25: #393b40,
    30: #43474e,        // --md-sys-color-on-surface-variant (light) - FIGMA EXACT
    35: #505257,
    40: #5c5e63,
    50: #73777f,        // --md-sys-color-outline (light) - FIGMA EXACT
    60: #8d9199,        // --md-sys-color-outline (dark) - FIGMA EXACT
    70: #a9abb1,
    80: #c3c6cf,        // --md-sys-color-on-surface-variant (dark) - FIGMA EXACT
    87: #c3c6cf,        // --md-sys-color-outline-variant (light) - FIGMA EXACT
    90: #e0e2e8,        // --md-sys-color-on-surface (dark) - FIGMA EXACT
    92: #e0e2e8,        // --md-sys-color-surface-container-highest (light) - FIGMA EXACT
    94: #e6e8ee,        // --md-sys-color-surface-container-high (light) - FIGMA EXACT
    95: #eceef4,        // --md-sys-color-surface-container (light) - FIGMA EXACT
    96: #f1f3f9,        // --md-sys-color-surface-container-low (light) - FIGMA EXACT
    98: #f7f9ff,        // --md-sys-color-surface (light) - FIGMA EXACT
    99: #fafbff,        // --md-sys-color-surface-light (light) - FIGMA EXACT
    100: #ffffff,       // --md-sys-color-surface-container-lowest (light) - FIGMA EXACT
  ),
  neutral-variant: (
    0: #000000,
    10: #161c23,
    20: #2b3139,
    25: #363c44,
    30: #42474f,
    35: #4d535b,
    40: #595f67,
    50: #727780,
    60: #8c919a,
    70: #a6abb5,
    80: #c2c7d1,
    90: #dee3ed,
    95: #ecf1fb,
    98: #f8f9ff,
    99: #fdfcff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0007,
    30: #93000a,
    35: #a80710,
    40: #ba1a1a,
    50: #de3730,
    60: #ff5449,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes, neutral-variant),
  error: map.get($_palettes, error),
);

$_primary: map.merge(map.get($_palettes, primary), $_rest);
$_tertiary: map.merge(map.get($_palettes, tertiary), $_rest);

// ========================================
// THEME DEFINITIONS USING FIGMA COLORS
// ========================================

$light-theme: mat.define-theme(
  (
    color: (
      theme-type: light,
      primary: $_primary,
      tertiary: $_tertiary,
    ),
    typography: (
      brand-family: 'Roboto',
    ),
  )
);

$dark-theme: mat.define-theme(
  (
    color: (
      theme-type: dark,
      primary: $_primary,
      tertiary: $_tertiary,
    ),
    typography: (
      brand-family: 'Roboto',
    ),
  )
);


