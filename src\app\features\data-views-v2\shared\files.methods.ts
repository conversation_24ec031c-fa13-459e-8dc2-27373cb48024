import {
  DataViewFile,
  DirectoryEntry,
  FileDirectoryEntry,
  FileEntry,
} from './data-view.interfaces';
import { MIME_TYPE_MAPPING } from './models';

export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = (reader.result as string).split(',')[1];
      resolve(base64);
    };
    reader.onerror = error => reject(error);
    reader.readAsDataURL(file);
  });
}

export function addFileToDirectory(
  directory: DirectoryEntry,
  relativePath: string,
  base64: string,
  directoryId: number,
) {
  const parts = relativePath.split('/');
  let currentDirectory = directory;

  for (const part of parts.slice(0, -1)) {
    let subDir = currentDirectory.children.find(
      child => child.type === 'directory' && child.name === part,
    ) as DirectoryEntry;
    if (!subDir) {
      subDir = {
        id: directoryId++,
        type: 'directory',
        name: part,
        children: [],
      };
      currentDirectory.children.push(subDir);
    }
    currentDirectory = subDir;
  }

  currentDirectory.children.push({
    id: directoryId++,
    type: 'file',
    name: parts[parts.length - 1],
    path: base64,
  });
}

export async function getFileBlob(
  fileEntry: FileEntry | DirectoryEntry,
): Promise<Blob | null> {
  console.log(fileEntry);
  if (fileEntry && fileEntry.path) {
    const base64Data = fileEntry.path;
    const fileName = fileEntry.name;
    try {
      const fileExtension = fileName.split('.').pop()?.toLowerCase();
      let mimeType = 'application/octet-stream';
      switch (fileExtension) {
        case 'csv':
          mimeType = 'text/csv';
          break;
        case 'parquet':
          mimeType = 'application/octet-stream';
          break;
        case 'xml':
          mimeType = 'application/xml';
          break;
        case 'yaml':
        case 'yml':
          mimeType = 'application/x-yaml';
          break;
        case 'tsv':
          mimeType = 'text/tab-separated-values';
          break;
        case 'txt':
          mimeType = 'text/plain';
          break;
        case 'png':
          mimeType = 'image/png';
          break;
        case 'jpeg':
        case 'jpg':
          mimeType = 'image/jpeg';
          break;
        default:
          mimeType = 'application/octet-stream';
      }
      const byteCharacters = atob(base64Data);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: mimeType });

      return blob;
    } catch (error) {
      console.error('Error creating Blob from base64:', error);
      return null;
    }
  }
  return null;
}

export function readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as ArrayBuffer);
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
}

export function convertFileEntryToFile(
  fileEntry: FileSystemFileEntry,
): Promise<DataViewFile> {
  return new Promise<DataViewFile>((resolve, reject) => {
    try {
      fileEntry.file(file =>
        resolve({
          file,
          fullPath:
            fileEntry?.fullPath
              ?.replace(/^\/+/, '')
              .split('/')
              .slice(0, -1)
              .join('/') ?? '',
        }),
      );
    } catch (error) {
      reject(error);
    }
  });
}

export function updateHierarchy(
  existingFiles: FileDirectoryEntry[],
  fullPath: string,
  incomingFiles: FileDirectoryEntry[],
  isPaginated = false,
): FileDirectoryEntry[] {
  // Transform incoming files by adding computed path
  const transformedIncoming = incomingFiles.map(file => ({
    ...file,
    path: isPaginated
      ? fullPath == ''
        ? '/'
        : `${fullPath}/${file.name}`
      : fullPath == ''
        ? `${fullPath}`
        : `${fullPath}/${file.name}`,
  }));

  return [...existingFiles, ...transformedIncoming].sort((a, b) =>
    a.type === 'folder' ? 1 : b.type === 'folder' ? -1 : 0,
  );
}

export function checkDuplicateInHierarchy(
  newItem: File,
  targetFolderPath: string,
  hierarchyObj: Record<number, FileDirectoryEntry[]>,
  itemType = 'file',
) {
  // Extract the actual array from the object
  // Get the array value from the object
  const hierarchy = Object.values(hierarchyObj).find(value =>
    Array.isArray(value),
  ) as FileDirectoryEntry[];

  if (!Array.isArray(hierarchy)) {
    console.error('No valid hierarchy array found, returning false');
    return false;
  }

  // Normalize the target folder path
  const normalizedTargetPath =
    targetFolderPath === '' ? '/' : `/${targetFolderPath}`;

  // Construct the expected full path for the new item
  const expectedPath =
    normalizedTargetPath === '/'
      ? `/`
      : `${normalizedTargetPath}/${newItem.name}`;

  // Check if any item has exact path match and correct type
  const isDuplicate = hierarchy.some(item => {
    const pathMatch = item.path === expectedPath;
    const typeMatch = item.type === itemType;
    const nameMatch = item.name === newItem.name;

    return pathMatch && typeMatch && nameMatch;
  });

  return isDuplicate;
}

export function validFileType(newItem: File): boolean {
  const fileName = newItem.name.toLowerCase();
  const extension = fileName.substring(fileName.lastIndexOf('.') + 1);
  return extension in MIME_TYPE_MAPPING;
}
