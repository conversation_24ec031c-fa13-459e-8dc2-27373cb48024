/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@angular/core';
import {
  Http<PERSON><PERSON>,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { ErrorService } from '../services/error.service';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(
    private toastr: ToastrService,
    private router: Router,
    private errorService: ErrorService,
  ) {}

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler,
  ): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: unknown) => {
        if (error instanceof HttpErrorResponse) {
          let message = '';
          message = error.error.errors.error;
          if (message) {
            this.toastr.error(message);
            return throwError(() => error);
          }

          // Get error message for user display
          message = this.errorService.getErrorMessage(error.status);
          this.toastr.error(message);

          // Handle 401 Unauthorized errors
          if (error.status === 401) {
            console.warn('401 Unauthorized - Redirecting to login...');
            localStorage.clear();

            // Force navigation to login (with route reinitialization)
            this.router
              .navigateByUrl('/auth', { skipLocationChange: true })
              .then(() => {
                this.router.navigate(['/auth/login']);
              });
          }

          return throwError(() => error); // Re-throw the error for further handling
        } else {
          console.error('Unknown error type:', error);
          return throwError(() => error);
        }
      }),
    );
  }
}

// To test go to console under inspect and paste localStorage.setItem('access_token', 'eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyNDAwMDB9.JxOas1u_1JbF1_01ru7gDPO3P_3wBa_FJcRQp5rpmc0');
// This throws unauthorized error
