import {
  CollectionViewer,
  DataSource,
  SelectionChange,
} from '@angular/cdk/collections';
import { FlatTreeControl } from '@angular/cdk/tree';
import { inject, Injectable, signal } from '@angular/core';
import { BehaviorSubject, map, merge, Observable } from 'rxjs';
import { DataViewComponentStore } from '../../component-store/data-view.store';
import { FileDirectoryEntry } from '../data-view.interfaces';

export class DynamicFlatNode {
  constructor(
    public item: FileDirectoryEntry,
    public level = 1,
    public expandable = false,
    public isLoading = signal(false),
    public isLoadMore = false,
  ) {}
}

/**
 * Database for dynamic data. When expanding a node in the tree, the data source will need to fetch
 * the descendants data from the database.
 */
@Injectable({ providedIn: 'root' })
export class DynamicDatabase {
  // Flat array of all files/folders
  initialData(hierarchyData: FileDirectoryEntry[]): DynamicFlatNode[] {
    const defaultNode: FileDirectoryEntry = {
      name: localStorage.getItem('project_id')?.toString() || '',
      path: '',
      type: 'folder',
      id: 0,
      parentPath: '/',
    };
    let rootNodes: FileDirectoryEntry[] = [];

    if (hierarchyData) {
      rootNodes = hierarchyData.filter(node => {
        return node.path === '' || node.path.split('/').length === 1;
      });
    }
    const allNodes = [defaultNode, ...rootNodes];

    return allNodes.map(
      node => new DynamicFlatNode(node, 0, this.isExpandable(node)),
    );
  }

  /** Returns children of a folder node */
  getChildren(
    node: FileDirectoryEntry,
    hierarchyData: FileDirectoryEntry[],
  ): FileDirectoryEntry[] {
    const parentPath = node.path;
    return hierarchyData.filter(child => {
      // Check: child path must start with parentPath + '/'
      if (!child.path.startsWith(parentPath + '/')) return false;

      // Relative path should NOT contain further '/'
      const relativePath = child.path.slice(parentPath.length + 1); // skip parent/
      return !relativePath.includes('/');
    });
  }

  /** Check if a node is expandable (i.e., a folder) */
  isExpandable(node: FileDirectoryEntry): boolean {
    return node.type === 'folder';
  }
}

export class DynamicDataSource implements DataSource<DynamicFlatNode> {
  dataChange = new BehaviorSubject<DynamicFlatNode[]>([]);
  private store = inject(DataViewComponentStore);

  get data(): DynamicFlatNode[] {
    return this.dataChange.value;
  }
  set data(value: DynamicFlatNode[]) {
    this._treeControl.dataNodes = value;
    this.dataChange.next(value);
  }

  constructor(
    private _treeControl: FlatTreeControl<DynamicFlatNode>,
    private _database: DynamicDatabase,
  ) {}

  connect(collectionViewer: CollectionViewer): Observable<DynamicFlatNode[]> {
    this._treeControl.expansionModel.changed.subscribe(change => {
      if (
        (change as SelectionChange<DynamicFlatNode>).added ||
        (change as SelectionChange<DynamicFlatNode>).removed
      ) {
        this.handleTreeControl(change as SelectionChange<DynamicFlatNode>);
      }
    });

    return merge(collectionViewer.viewChange, this.dataChange).pipe(
      map(() => this.data),
    );
  }

  disconnect(): void {
    // to disconnect
  }

  /** Handle expand/collapse behaviors */
  handleTreeControl(change: SelectionChange<DynamicFlatNode>) {
    if (change.added) {
      change.added.forEach(node => this.toggleNode(node, true));
    }
    if (change.removed) {
      change.removed
        .slice()
        .reverse()
        .forEach(node => this.toggleNode(node, false));
    }
  }

  /**
   * Toggle the node, remove from display list
   */
  async toggleNode(node: DynamicFlatNode, expand: boolean) {
    const projectId = this.store.project_id();
    node.isLoading.set(true);

    // Step 1: Start loading
    const loadedPaths = this.store.loadedPaths();
    this.store.setNewlyAddedFiles(0, true);
    const alreadyLoaded = loadedPaths.includes(
      node.item.path.startsWith('/') ? node.item.path.slice(1) : node.item.path,
    );
    this.store.setSelectedFolderPath(
      node.item.path.startsWith('/') ? node.item.path.slice(1) : node.item.path,
    );
    if (!alreadyLoaded) {
      await this.store.loadFiles(projectId, false, node.item.path);
      await this.waitForSignalToBeFalse(() => this.store.isLoading());
    }
    // Step 2: Wait until store is no longer loading
    await this.waitForSignalToBeFalse(() => this.store.isLoading());

    // Step 3: Get latest data
    const updatedData = this.store.hierarchy()[projectId];
    const children = this._database.getChildren(node.item, updatedData);
    const index = this.data.indexOf(node);
    if (!children || index < 0) return;

    // Step 4: Expand/collapse logic
    if (expand) {
      const nodes = children.map(
        child =>
          new DynamicFlatNode(
            child,
            node.level + 1,
            this._database.isExpandable(child),
          ),
      );
      this.data.splice(index + 1, 0, ...nodes);
    } else {
      let count = 0;

      for (
        let i = index + 1;
        i < this.data.length && this.data[i].level > node.level;
        i++, count++
      ) {
        // counting child nodes to remove
      }
      this.data.splice(index + 1, count);
    }

    // Step 5: Notify and reset loading
    this.dataChange.next(this.data);
    node.isLoading.set(false);
  }

  waitForSignalToBeFalse(signal: () => boolean): Promise<void> {
    return new Promise(resolve => {
      const interval = setInterval(() => {
        if (!signal()) {
          clearInterval(interval);
          resolve();
        }
      }, 50); // poll every 50ms
    });
  }

  loadMore(node: DynamicFlatNode) {
    const parentPath = node.item.parentPath;
    const loadMoreIndex = this.data.indexOf(node);
    if (loadMoreIndex >= 0) {
      this.data.splice(loadMoreIndex, 1);
    }

    const projectId = this.store.project_id();
    this.store
      .loadMoreFiles(
        projectId,
        false,
        parentPath,
        2,
        node.item.totalItems || 10,
      )
      .then(() => {
        const updatedData = this.store.hierarchy()[projectId];
        const nextItems = this.paginateLevel(node, updatedData);

        this.data.splice(loadMoreIndex, 0, ...nextItems);
        this.dataChange.next(this.data);
      });
  }

  private paginateLevel(
    parentNode: DynamicFlatNode,
    allChildren: FileDirectoryEntry[],
  ): DynamicFlatNode[] {
    const paginatedFiles = this.store.newAddedFileIds();
    const directChildren = allChildren.filter((child: FileDirectoryEntry) => {
      return paginatedFiles.includes(child.id);
    });
    this.store.setFiles(
      allChildren.filter(node => node.type !== 'loadMore'),
      this.store.project_id(),
    );
    const nextItems = directChildren.map(
      child =>
        new DynamicFlatNode(
          child,
          parentNode.level,
          this._database.isExpandable(child),
        ),
    );
    this.store.setNewlyAddedFiles(0, true);
    return nextItems;
  }
}
