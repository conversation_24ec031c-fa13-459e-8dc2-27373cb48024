import { Component, OnInit } from '@angular/core';
import { SharedModule } from '../../../shared/shared.module';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { g_const } from '../../../_utility/global_const';
import { CommonModule } from '@angular/common';
import { UpgradePlanModalComponent } from '../upgrade-plan-modal/upgrade-plan-modal.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

import { Clipboard } from '@angular/cdk/clipboard';
import { Router } from '@angular/router';
import { AuthService } from '../../auth/services/auth.service';
import { FormsModule } from '@angular/forms';
import { StripeService } from '../services/stripe.service';
import { ToastrService } from 'ngx-toastr';
import { UsageComponent } from './usage/usage.component';

@Component({
  selector: 'app-settings',
  imports: [
    SharedModule,
    CommonModule,
    MatListModule,
    MatDividerModule,
    MatTabsModule,
    MatDialogModule,
    MatChipsModule,
    MatButtonModule,
    MatProgressBarModule,
    MatIconModule,
    FormsModule,
    UsageComponent,
  ],
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.css'],
})
export class SettingsComponent implements OnInit {
  g_const = g_const;
  activeSection = 'loginSecurity';
  email = '';
  // isUpgradePlanVisible = false;
  subscriptionNextPage = '';

  constructor(
    private dialog: MatDialog,
    private clipboard: Clipboard,
    private authService: AuthService,
    private router: Router,
    private stripeService: StripeService,
    private toastrService: ToastrService,
  ) {}
  ngOnInit(): void {
    this.getUserEmail();
    // Stripe initialization and subscription status verification are deferred until user interaction
  }
  // Function to trigger modal
  openModal(): void {
    this.dialog.open(UpgradePlanModalComponent, {
      panelClass: 'custom-dialog-container', // Custom class
      //width: '90%', // Adjust the modal width as needed
      //height: 'auto',
      maxWidth: '95%',
      data: { showInfo: false },
    });
  }

  openInfoModal(): void {
    this.dialog.open(UpgradePlanModalComponent, {
      panelClass: 'custom-dialog-container',
      maxWidth: '95%',
      data: { showInfo: true },
    });
  }

  // General function to copy value from any input
  copyToClipboard(value: string): void {
    this.clipboard.copy(value);
  }

  changeSection(section: string) {
    this.activeSection = section;

    // Only verify subscription status when navigating to billing-related sections
    if (section === 'planBilling') {
      this.verifySubscriptionStatus();
    }
  }
  getUserEmail(): void {
    this.email = localStorage.getItem('email') || '';
  }
  onDelete(): void {
    this.authService.deleteUser().subscribe({
      next: () => this.redirectToLogin(),
      error: () => this.redirectToLogin(),
    });
  }
  redirectToLogin(): void {
    localStorage.clear();
    this.router.navigate(['auth/login']);
  }

  /**
   *  Initialize Stripe.
   */
  async initializeStripe() {
    try {
      await this.stripeService.initializeStripe();
    } catch (error) {
      console.error('Error initializing Stripe:', error);
    }
  }

  /**
   *  Redirect user to the Billing Portal.
   */
  async goToBillingPortal(): Promise<void> {
    try {
      // Stripe will be initialized only when this method is called
      await this.stripeService.goToBillingPortal();
    } catch (error) {
      this.toastrService.error(
        'An error occured while redirecting to billing portal. Please try again later.',
      );
      console.error('HTTP error redirecting billing portal:', error);
    }
  }

  /**
   * To verfify the subscription status.
   * @returns
   */
  async verifySubscriptionStatus(): Promise<void> {
    try {
      const status = await this.stripeService.verifySubscriptionStatus();
      if (status) {
        this.subscriptionNextPage = status?.next_page;
        // this.isUpgradePlanVisible = status.is_active;
      }
    } catch (error) {
      console.error(error);
    }
  }
}
