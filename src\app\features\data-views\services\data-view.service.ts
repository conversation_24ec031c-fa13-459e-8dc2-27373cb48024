import { Injectable } from '@angular/core';
import { environment } from '../../../env/env';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { TableDataWithPagination } from '../../../_models/common.model';
import { Observable } from 'rxjs';
import {
  ProjectData,
  UploadedFile,
} from '../../dashborad/models/project.model';
import { BackendResponse } from '../../../_models/visual-data/visual-data.model';
import { Folder } from '../../../_models/visual-data/plot.model';
import {
  BatchUploadResult,
  ColumnAddData,
  ColumnChoices,
  ColumnDataType,
  ColumnIDSetData,
  ColumnInfo,
  ColumnValuesData,
  DataViewBackendResponse,
  DynamicAddColumn,
  DynamicStatistics,
  FileUploadUrl,
  FileData,
  FileMetadata,
  FileUploadProgress,
  FileUploadStatus,
  FilterColumnData,
  FilterData,
  FolderStatus,
  FolderStructure,
  ProcessFileStatusInterface,
  RenameData,
  StatisticsData,
  StorageAddonInterface,
} from '../models/data-view.model';
import { StandardPlan } from '../../../_models/plan.model';

interface DataViewData {
  projectId: number;
  fileId: number;
  idColumn: string;
  folderId: number;
}

@Injectable({
  providedIn: 'root',
})
export class DataviewService {
  constructor(private http: HttpClient) {}

  private dataviewUrl = `${environment.apiUrl}`;

  storeDataViewInLocalStorage(
    projectId: number,
    folderId: number,
    file: UploadedFile,
  ): void {
    localStorage.setItem('fileId', file.id.toString());
    const data: DataViewData = {
      projectId: projectId,
      folderId: folderId,
      fileId: file.id,
      idColumn: file.id_column,
    };
    localStorage.setItem('data_view', JSON.stringify(data));
  }

  getProjectName(project_id: number): Observable<BackendResponse<ProjectData>> {
    return this.http.get<BackendResponse<ProjectData>>(
      `${this.dataviewUrl}projects/${project_id}/?list_files=false`,
      { headers: this.getHeaders() },
    );
  }

  removeDataViewInLocalStorage(): void {
    localStorage.removeItem('fileId');
    localStorage.removeItem('data_view');
  }

  updateFileName(
    fileName: string,
    file_id: number,
  ): Observable<DataViewBackendResponse<RenameData>> {
    return this.http.put<DataViewBackendResponse<RenameData>>(
      `${this.dataviewUrl}dataview/file/${file_id}/rename/`,
      { new_name: fileName },
      { headers: this.getHeaders() },
    );
  }

  getDataViewFromLocalStorage(): DataViewData | null {
    const dataString = localStorage.getItem('data_view');
    if (dataString !== null) {
      const dataViewData: DataViewData = JSON.parse(dataString);
      return dataViewData;
    }
    return null;
  }

  getDisplayData(
    file_id: number,
    page: number,
    page_size: number,
  ): Observable<TableDataWithPagination> {
    return this.http.get<TableDataWithPagination>(
      `${this.dataviewUrl}/displayFile/${file_id}/?page=${page}&page_size=${page_size}&processed=true`,
      {},
    );
  }

  getFilterData(
    folder_id: number,
    text_search: string,
  ): Observable<BackendResponse<Folder[]>> {
    return this.http.get<BackendResponse<Folder[]>>(
      `${this.dataviewUrl}projects/projects/${folder_id}/files/filter/?text_contains=${text_search}&search_files=true`,
      { headers: this.getHeaders() },
    );
  }

  getFilteredDataByDate(
    folder_id: number,
    min_date: string,
    max_date: string,
  ): Observable<BackendResponse<Folder[]>> {
    return this.http.get<BackendResponse<Folder[]>>(
      `${this.dataviewUrl}projects/projects/${folder_id}/files/filter/?search_files=true&min_created_at=${min_date}&max_created_at=${max_date}`,
      { headers: this.getHeaders() },
    );
  }

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json',
    });
  }

  uploadFileData(
    file_id: string,
    file_name: { filename: string },
    size: number,
  ): Observable<DataViewBackendResponse<FileUploadUrl>> {
    return this.http.post<DataViewBackendResponse<FileUploadUrl>>(
      `${this.dataviewUrl}files/presigned-url/`,
      [{ file_name: file_name.filename, size: size }],
      {
        headers: this.getHeaders(),
      },
    );
  }

  //TODO is not in use stille relevant?
  //uploadFolderData(folder_id: string, folder_path: any): Observable<any> {
  //  return this.http.post<any>(
  //    `${this.dataviewUrl}files/files/upload-folder/${folder_id}/`,
  //    folder_path,
  //    {
  //      headers: this.getHeaders(),
  //    },
  //  );
  //}

  toCheckTheFolderStatus(
    folder_structure: FolderStructure,
  ): Observable<DataViewBackendResponse<FolderStatus>> {
    const folder_id = folder_structure.folder_structure.folder_id;
    const params = folder_structure.folder_structure;
    const headers = this.getHeaders();
    return this.http.post<DataViewBackendResponse<FolderStatus>>(
      `${this.dataviewUrl}files/files/upload-folder/${folder_id}/status/`,
      { folder_structure: params },
      { headers: headers },
    );
  }

  processBatch(
    fields: number[],
  ): Observable<DataViewBackendResponse<BatchUploadResult>> {
    const headers = this.getHeaders();
    return this.http.post<DataViewBackendResponse<BatchUploadResult>>(
      `${this.dataviewUrl}files/files/process/batch/`,
      { file_ids: fields },
      { headers: headers },
    );
  }

  toGetFileData(
    file_id: number,
  ): Observable<DataViewBackendResponse<FileUploadProgress>> {
    const headers = this.getHeaders();
    return this.http.post<DataViewBackendResponse<FileUploadProgress>>(
      `${this.dataviewUrl}files/folder/process/${file_id}/`,
      {},
      { headers: headers },
    );
  }

  toGetColumnDataTypes(): Observable<ColumnDataType> {
    const headers = this.getHeaders();
    return this.http
      .get<ColumnDataType>(
        `${this.dataviewUrl}appoptions/DropDown/datatypes/`,
        {
          headers,
        },
      )
      .pipe();
  }

  toGetColumnsData(
    file_id: number,
  ): Observable<DataViewBackendResponse<ColumnInfo>> {
    const headers = this.getHeaders();
    return this.http
      .get<
        DataViewBackendResponse<ColumnInfo>
      >(`${this.dataviewUrl}files/ColumnInfo/${file_id}/?exclude_id=true`, { headers })
      .pipe();
  }

  PreviewFiles(file_id: number): Observable<BackendResponse<FileData>> {
    const headers = this.getHeaders();
    return this.http
      .get<
        BackendResponse<FileData>
      >(`${this.dataviewUrl}dataview/display/file/${file_id}/?page=1&preview=true&processed=true`, { headers })
      .pipe();
  }

  ViewFileData(
    file_id: string,
    fileSize = 10,
    page = 1,
  ): Observable<BackendResponse<FileData>> {
    const headers = this.getHeaders();
    return this.http
      .get<
        BackendResponse<FileData>
      >(`${this.dataviewUrl}dataview/display/file/${file_id}/?page=${page}&page_size=${fileSize}&processed=true`, { headers })
      .pipe();
  }

  setFileMetaData(
    file_id: number,
    body: unknown,
  ): Observable<DataViewBackendResponse<FileMetadata>> {
    return this.http.post<DataViewBackendResponse<FileMetadata>>(
      `${this.dataviewUrl}dataview/set-file-metdadata/${file_id}/`,
      body, // Pass the body here
      { headers: this.getHeaders() },
    );
  }

  //TODO not in use still relevant?
  //SavedIdCloumns(file_id: number): Observable<any> {
  //  const headers = this.getHeaders();
  //  return this.http.post<any>(
  //    `${this.dataviewUrl}dataview/set-column-ID/${file_id}/?column_name=id`,
  //    '',
  //    { headers: headers },
  //  );
  //}

  DeleteFiles(file_id: number): Observable<BackendResponse<null>> {
    const headers = this.getHeaders();
    return this.http
      .delete<
        BackendResponse<null>
      >(`${this.dataviewUrl}files/${file_id}/`, { headers })
      .pipe();
  }

  DeleteFolder(folder_id: number): Observable<BackendResponse<null>> {
    const headers = this.getHeaders();
    return this.http
      .delete<BackendResponse<null>>(
        `${this.dataviewUrl}files/folders/${folder_id}/`,
        {
          headers,
        },
      )
      .pipe();
  }

  //TODO is not used still relevant?
  //UpdateColumns(): Observable<any> {
  //  const headers = this.getHeaders();
  //  return this.http
  //    .post<any>(`${this.dataviewUrl}files/ColumnInfo/1366/?update_all=True`, {
  //      headers,
  //    })
  //    .pipe();
  //}

  getAllDataView(
    folder_id: string,
    page = 1,
    page_size = 10,
    list_files: boolean,
    sortOption?: string,
  ): Observable<BackendResponse<Folder>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', page_size.toString())
      .set('list_files', list_files.toString());

    if (sortOption) {
      const sort_order = 'desc';
      params = params
        .set('sort_by', sortOption.toString())
        .set('sort_order', sort_order.toString());
    }

    return this.http.get<BackendResponse<Folder>>(
      `${this.dataviewUrl}files/hierarchy/`,
      {
        headers: this.getHeaders(),
        params: params,
      },
    );
  }

  signedUrlUpload(signedUrl: string, binaryData: Blob): Observable<null> {
    return this.http.put<null>(signedUrl, binaryData);
  }

  checkFileUploadStatus(
    folder_id: string,
    file_name: {
      filename: string;
    },
  ): Observable<DataViewBackendResponse<FileUploadStatus>> {
    return this.http.post<DataViewBackendResponse<FileUploadStatus>>(
      `${this.dataviewUrl}files/folder/${folder_id}/upload-status/`,
      file_name,
      {
        headers: this.getHeaders(),
      },
    );
  }

  checkFileProcess(file_id: number): Observable<FileUploadProgress> {
    return this.http.post<FileUploadProgress>(
      `${this.dataviewUrl}files/folder/process/${file_id}/`,
      {},
      {
        headers: this.getHeaders(),
      },
    );
  }

  checkProcessFileStatus(
    taskId: string,
  ): Observable<ProcessFileStatusInterface> {
    return this.http.get<ProcessFileStatusInterface>(
      `${this.dataviewUrl}files/folder/file-status/${taskId}`,
      {
        headers: this.getHeaders(),
      },
    );
  }

  DownloadFileData(file_id: number | null): Observable<Blob> {
    const headers = this.getHeaders().set('Content-Type', 'text/csv');
    return this.http.get(
      `${this.dataviewUrl}dataview/download/file/${file_id}/`,
      {
        headers,
        responseType: 'blob',
      },
    );
  }

  DownloadFileStatisticsData(
    file_id: number | null,
    typeOfStatistics?: string,
    processed?: boolean,
  ): Observable<Blob> {
    const headers = this.getHeaders().set('Content-Type', 'text/csv');
    return this.http.get(
      `${this.dataviewUrl}dataview/download/data/statistics/${file_id}/?type=${typeOfStatistics}&processesed=${processed}`,
      {
        headers,
        responseType: 'blob',
      },
    );
  }

  DownloadImageStatisticsData(file_id: number | null): Observable<Blob> {
    const headers = this.getHeaders();
    return this.http.get(
      `${this.dataviewUrl}dataview/downloadImageStatsistics/${file_id}/?page=1&page_size=3`,
      {
        headers,
        responseType: 'blob',
      },
    );
  }

  getNextPageByUrl(url: string): Observable<BackendResponse<FileData>> {
    const headers = this.getHeaders();
    return this.http.get<BackendResponse<FileData>>(url, { headers });
  }

  SortColumn(
    file_id: number | null,
    page: number,
    page_size: number,
    column: string,
    order: string,
  ): Observable<DataViewBackendResponse<FilterColumnData>> {
    return this.http.post<DataViewBackendResponse<FilterColumnData>>(
      `${this.dataviewUrl}dataview/filter/data/${file_id}/?page=${page}&page_size=${page_size}&sort_column=${column}&sort_order=${order}&processed=true`,
      '',
      { headers: this.getHeaders() },
    );
  }

  FilterColumn(
    file_id: number | null,
    filters: string,
    page: number,
    page_size: number,
  ): Observable<DataViewBackendResponse<FilterColumnData>> {
    return this.http.post<DataViewBackendResponse<FilterColumnData>>(
      `${this.dataviewUrl}dataview/filter/data/${file_id}/?page=${page}&page_size=${page_size}&processed=true`,
      filters,
      { headers: this.getHeaders() },
    );
  }

  // TODO not used - still relevant?
  // GetFilterOptions(filterId: number | null): Observable<any> {
  //   return this.http.get<any>(
  //     `${this.dataviewUrl}dataview/filter/${filterId}/`,
  //     { headers: this.getHeaders() },
  //   );
  // }
  GetFilter(filterId: number | null): Observable<BackendResponse<FilterData>> {
    return this.http.get<BackendResponse<FilterData>>(
      `${this.dataviewUrl}dataview/filter/file/${filterId}/`,
      { headers: this.getHeaders() },
    );
  }

  RemoveFilter(
    filterId: number | null,
  ): Observable<DataViewBackendResponse<null>> {
    return this.http.delete<DataViewBackendResponse<null>>(
      `${this.dataviewUrl}dataview/filter/${filterId}/`,
      { headers: this.getHeaders() },
    );
  }

  randomFileData(
    file_id: number | null,
    page: number,
    page_size: number,
    random: boolean,
  ): Observable<BackendResponse<FileData>> {
    return this.http.get<BackendResponse<FileData>>(
      `${this.dataviewUrl}dataview/display/file/${file_id}/?page=${page}&page_size=${page_size}&processed=true&random=${random}`,
      { headers: this.getHeaders() },
    );
  }

  getStatisticalValues(
    file_id: number | null,
    type: string,
    processed = false,
  ): Observable<DataViewBackendResponse<StatisticsData>> {
    return this.http.post<DataViewBackendResponse<StatisticsData>>(
      `${this.dataviewUrl}dataview/data/statistics/${file_id}/?type=${type}&processed=${processed}`,
      '',
      { headers: this.getHeaders() },
    );
  }

  getColumnChoice(
    file_id: number | null,
  ): Observable<DataViewBackendResponse<ColumnChoices>> {
    return this.http.get<DataViewBackendResponse<ColumnChoices>>(
      `${this.dataviewUrl}dataview/column-choices/${file_id}`,
      { headers: this.getHeaders() },
    );
  }

  getColumnValues(
    file_id: number | null,
  ): Observable<DataViewBackendResponse<ColumnValuesData>> {
    return this.http.get<DataViewBackendResponse<ColumnValuesData>>(
      `${this.dataviewUrl}dataview/column-values/${file_id}`,
      { headers: this.getHeaders() },
    );
  }

  AddColumn(
    file_id: number | null,
    operations: DynamicAddColumn[],
    referenceColumn?: string,
    position?: string,
  ): Observable<DataViewBackendResponse<ColumnAddData>> {
    const headers = this.getHeaders();

    const modifiedOperations = operations.map(operation => {
      if (referenceColumn) {
        operation.reference_column = referenceColumn;
      }
      if (position) {
        operation.position = position;
      }
      return operation;
    });

    const body = {
      operations: modifiedOperations,
    };

    return this.http.post<DataViewBackendResponse<ColumnAddData>>(
      `${this.dataviewUrl}dataview/math-advanced/${file_id}/`,
      body,
      { headers: headers },
    );
  }

  DeleteColumn(
    file_id: number | null,
    columnName: string,
  ): Observable<DataViewBackendResponse<null>> {
    const headers = this.getHeaders();
    const body = {
      column_name: columnName,
    };

    const options = {
      headers: headers,
      body: body,
    };

    return this.http.delete<DataViewBackendResponse<null>>(
      `${this.dataviewUrl}dataview/delete-cols/${file_id}/`,
      options,
    );
  }

  getColumnID(
    file_id: number | null,
  ): Observable<DataViewBackendResponse<Record<string, string[]>>> {
    return this.http.get<DataViewBackendResponse<Record<string, string[]>>>(
      `${this.dataviewUrl}dataview/column-ID/${file_id}`,
      { headers: this.getHeaders() },
    );
  }

  updateColumnID(
    file_id: number | null,
    column: string,
  ): Observable<DataViewBackendResponse<ColumnIDSetData>> {
    return this.http.post<DataViewBackendResponse<ColumnIDSetData>>(
      `${this.dataviewUrl}dataview/set-column-ID/${file_id}/?column_name=${column}`,
      '',
      { headers: this.getHeaders() },
    );
  }

  getStatisticsData(
    image_file_id: number | null,
    page: number,
    page_size: number,
  ): Observable<DataViewBackendResponse<DynamicStatistics>> {
    const headers = this.getHeaders();
    return this.http.post<DataViewBackendResponse<DynamicStatistics>>(
      `${this.dataviewUrl}dataview/image/statistics/${image_file_id}/?page=${page}&page_size=${page_size}&show_fig=True`,
      '',
      {
        headers,
      },
    );
  }

  downloadSinglePlot(
    image_file_id: number | null,
    plotname: string,
  ): Observable<Blob> {
    const headers = this.getHeaders().set('Content-Type', 'text/csv');
    return this.http.get(
      `${this.dataviewUrl}dataview/download/data/statistics/image-plot/${image_file_id}/?show_fig=false&plot_name=${plotname}`,
      {
        headers,
        responseType: 'blob',
      },
    );
  }

  sortDataView(
    folder_id: string | null,
    page = 1,
    page_size = 10,
    list_files: boolean,
    sortOption: string,
    sort_order: string,
  ): Observable<BackendResponse<Folder>> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', page_size.toString())
      .set('list_files', list_files.toString())
      .set('sort_by', sortOption.toString())
      .set('sort_order', sort_order.toString());

    return this.http.get<BackendResponse<Folder>>(
      `${this.dataviewUrl}files/hierarchy/`,
      {
        headers: this.getHeaders(),
        params: params,
      },
    );
  }

  getSubscriptionPlans(): Observable<StandardPlan> {
    return this.http.get<StandardPlan>(
      `${this.dataviewUrl}subscriptions/plans/?storage_addon=true`,
      { headers: this.getHeaders() },
    );
  }

  storageAddon(stripe_id: string): Observable<StorageAddonInterface> {
    return this.http.post<StorageAddonInterface>(
      `${this.dataviewUrl}subscriptions/storage-addon/`,
      { addon_price_id: stripe_id },
      {
        headers: this.getHeaders(),
      },
    );
  }
}
