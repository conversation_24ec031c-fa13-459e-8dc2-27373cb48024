<div>
  <app-component-header [projectName]="title">
    <button
      mat-flat-button
      class="whitespace-nowrap text-ellipsis add-button"
      (click)="uploadFileFolder()">
      <mat-icon>add</mat-icon>
      Add Data
    </button>
    <app-data-view-sort
      class="sort"
      (sortOptionChanged)="sortOptionChanged($event)"></app-data-view-sort>
  </app-component-header>
  <div class="p-4 flex h-[77vh] flex-row mt-[28px]">
    @if (!isLoading()) {
      <div class="w-full flex overflow-y-auto">
        <div
          class="min-w-0 overflow-y-auto h-77"
          [ngClass]="{
            'w-full': !previewMetadata.showPreview,
            'w-1/2': previewMetadata.showPreview,
          }">
          <app-data-view-tree></app-data-view-tree>
        </div>

        <ng-container *ngIf="previewMetadata.showPreview">
          <div class="w-1/2 min-w-0 overflow-y-auto">
            <app-preview-table></app-preview-table>
          </div>
        </ng-container>
      </div>
    }
  </div>
</div>
