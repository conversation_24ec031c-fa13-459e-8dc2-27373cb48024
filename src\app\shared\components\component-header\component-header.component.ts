import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewChild,
  AfterViewInit,
  inject,
} from '@angular/core';
import { SearchOptions } from '../../../_models/common.model';
import { DataViewComponentStore } from '../../../features/data-views-v2/component-store/data-view.store';

interface ProjectModalContext {
  title: string;
  description: string;
  // Any other properties the template may need
}

@Component({
  selector: 'app-component-header',
  templateUrl: './component-header.component.html',
  styleUrl: './component-header.component.css',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ComponentHeaderComponent implements AfterViewInit {
  @ViewChild('projectModal') projectModal!: TemplateRef<ProjectModalContext>;
  @Input() projectName!: string;
  showTooltip = true;
  @Output() searchPerformed = new EventEmitter<SearchOptions>();
  @ViewChild('titleElement', { static: false }) titleElement!: ElementRef;
  readonly store = inject(DataViewComponentStore);

  constructor(private cdr: ChangeDetectorRef) {}
  ngAfterViewInit() {
    setTimeout(() => {
      const element = this.titleElement.nativeElement;
      this.showTooltip = element.scrollWidth > 450;
      this.cdr.detectChanges();
    }, 500);
  }

  onSearchPerformed(searchOptions: SearchOptions): void {
    this.store.resetPartialState();
    this.store.setSearchTerm(searchOptions.title ?? '');
    this.store.loadFiles(this.store.project_id(), true);
    this.searchPerformed.emit(searchOptions);
  }
}
