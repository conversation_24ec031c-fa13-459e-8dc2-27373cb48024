<div
  class="flex h-full py-1 pr-2 pl-1 bg-[#F7F9FF] dark:bg-gray-800 border-b [border-color:#79747E1F]">
  <!-- SUB MENU -->
  <div
    class="w-10 flex items-center justify-center bg-[#F7F9FF] dark:bg-gray-800">
    <button
      mat-icon-button
      [matMenuTriggerFor]="menu"
      aria-label="Example icon-button with a menu">
      <mat-icon class="text-black dark:text-white">more_vert</mat-icon>
    </button>
    <mat-menu #menu="matMenu">
      @if (this.plotData()?.favorite === false) {
        <button
          mat-menu-item
          (click)="visualDataStore.markUnmarkPlotFavPlot(this.plotId, true)">
          <mat-icon class="text-[#73777F] dark:text-white"
            >star_border</mat-icon
          >
          <span class="dark:text-white text-[#73777F]">Mark as Favorite</span>
        </button>
      } @else {
        <button
          mat-menu-item
          (click)="visualDataStore.markUnmarkPlotFavPlot(this.plotId, false)">
          <mat-icon class="text-[#73777F] dark:text-white">star</mat-icon>
          <span class="text-[#73777F] dark:text-white">Remove as Favorite</span>
        </button>
      }

      <button
        mat-menu-item
        (click)="visualDataStore.removePlotFromDb(this.plotId)">
        <mat-icon class="text-[#73777F] dark:text-white">delete</mat-icon>
        <span class="text-[#73777F] dark:text-white">Remove Plot</span>
      </button>

      <button mat-menu-item (click)="openPlotDataFilterModal()">
        <mat-icon class="text-[#73777F] dark:text-white">filter_list</mat-icon>
        <span class="text-[#73777F] dark:text-white">Filter Data</span>
      </button>
    </mat-menu>
  </div>

  <!-- DETAILS SECTION -->
  <div
    class="flex justify-center flex-col flex-1 min-w-0 bg-[#F7F9FF] dark:bg-gray-800 dark:text-white px-2">
    <div class="truncate overflow-hidden whitespace-nowrap">
      <span
        [matTooltip]="this.plotData()?.name"
        matTooltipPosition="right"
        matTooltipShowDelay="300"
        class="font-medium"
        >{{ this.plotData()?.name }}</span
      >
    </div>
    <div class="truncate overflow-hidden whitespace-nowrap text-[#73777F]">
      {{ this.plotData()?.file_name }}
    </div>
  </div>

  <!-- FULL SCREEN BUTTON -->
  <div
    class="flex items-center justify-center w-[48px] h-[48px] bg-[#F7F9FF] dark:bg-gray-800 dark:text-white">
    <button
      (click)="toggleFullScreenMode()"
      class="w-full h-full flex items-center justify-center">
      <mat-icon class="screen">fullscreen_exit </mat-icon>
    </button>
  </div>
</div>
