.mat-elevation-z8 {
  box-shadow: none;
  background-color: white;
}

.table-modal {
  background-color: #f7f9ff;
  padding: 20px;
}

.mat-table {
  border-spacing: 0;
}

.mat-cell {
  padding: 4px 8px;
}

.image-modal {
  background-color: #f7f9ff;
  padding: 20px;
}

.example-container {
  height: 420px;
  max-width: 100%;
  overflow: auto;
}

td.mat-column-star {
  width: 20px;
  padding-right: 8px;
}

th.mat-column-position,
td.mat-column-position {
  padding-left: 8px;
}

.mat-mdc-table-sticky-border-elem-right {
  border-left: 1px solid #e0e0e0;
}

.mat-mdc-table-sticky-border-elem-left {
  border-right: 1px solid #e0e0e0;
}

.mat-table {
  transition: none !important;
  animation: none !important;
}

.mat-row,
.mat-header-row {
  transition: none !important;
  animation: none !important;
}

td,
th {
  text-align: center;
  border: 1px solid #ecebf8;
}

td:last-child,
th:last-child {
  border-right: none;
}

table tr:nth-child(odd) {
  background-color: #f1f3f9;
}
table th {
  background-color: #f7f9ff;
}

td {
  color: #82868e;
  font-weight: 400;
}
