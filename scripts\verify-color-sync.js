#!/usr/bin/env node

/**
 * COLOR SYNC VERIFICATION SCRIPT
 * 
 * This script verifies that colors in m3-theme.scss are synchronized 
 * with the CSS custom properties in _color.scss.
 * 
 * Usage: node scripts/verify-color-sync.js
 */

const fs = require('fs');
const path = require('path');

// Color mappings that should be synchronized
const COLOR_MAPPINGS = {
  // Primary colors
  '#296197': ['primary (light)', 'SCSS: primary.40', 'CSS: --md-sys-color-primary (light)'],
  '#a1cafd': ['primary (dark)', 'SCSS: primary.80', 'CSS: --md-sys-color-primary (dark)'],
  '#ffffff': ['on-primary (light)', 'SCSS: primary.100', 'CSS: --md-sys-color-on-primary (light)'],
  '#003259': ['on-primary (dark)', 'SCSS: primary.20', 'CSS: --md-sys-color-on-primary (dark)'],
  '#d2e4ff': ['primary-container (light)', 'SCSS: primary.90', 'CSS: --md-sys-color-primary-container (light)'],
  '#1a4975': ['primary-container (dark)', 'SCSS: primary.75', 'CSS: --md-sys-color-primary-container (dark)'],
  
  // Surface colors
  '#f7f9ff': ['surface (light)', 'SCSS: neutral.98', 'CSS: --md-sys-color-surface (light)'],
  '#101418': ['surface (dark)', 'SCSS: neutral.6', 'CSS: --md-sys-color-surface (dark)'],
  '#181c20': ['on-surface (light)', 'SCSS: neutral.23', 'CSS: --md-sys-color-on-surface (light)'],
  '#e0e2e8': ['on-surface (dark)', 'SCSS: neutral.90', 'CSS: --md-sys-color-on-surface (dark)'],
  
  // Container colors
  '#eceef4': ['surface-container (light)', 'SCSS: neutral.95', 'CSS: --md-sys-color-surface-container (light)'],
  '#1c2024': ['surface-container (dark)', 'SCSS: neutral.17', 'CSS: --md-sys-color-surface-container (dark)'],
  
  // Secondary colors
  '#006874': ['secondary (light)', 'SCSS: secondary.40', 'CSS: --md-sys-color-secondary (light)'],
  '#82d3e0': ['secondary (dark)', 'SCSS: secondary.80', 'CSS: --md-sys-color-secondary (dark)'],
  
  // Tertiary colors
  '#67548e': ['tertiary (light)', 'SCSS: tertiary.40', 'CSS: --md-sys-color-tertiary (light)'],
  '#d2bcfd': ['tertiary (dark)', 'SCSS: tertiary.80', 'CSS: --md-sys-color-tertiary (dark)'],
};

function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`❌ Error reading ${filePath}:`, error.message);
    return null;
  }
}

function extractColorsFromScss(content) {
  const colors = new Set();
  // Match hex colors in SCSS format
  const hexMatches = content.match(/#[0-9a-fA-F]{6}/g);
  if (hexMatches) {
    hexMatches.forEach(color => colors.add(color.toLowerCase()));
  }
  return colors;
}

function extractColorsFromCss(content) {
  const colors = new Set();
  // Match hex colors in CSS custom properties
  const hexMatches = content.match(/#[0-9a-fA-F]{6}/g);
  if (hexMatches) {
    hexMatches.forEach(color => colors.add(color.toLowerCase()));
  }
  return colors;
}

function verifyColorSync() {
  console.log('🔄 FIGMA COLOR SYNC VERIFICATION\n');
  
  // Read files
  const scssContent = readFile('m3-theme.scss');
  const cssContent = readFile('src/styles/_color.scss');
  
  if (!scssContent || !cssContent) {
    console.error('❌ Failed to read required files');
    process.exit(1);
  }
  
  // Extract colors
  const scssColors = extractColorsFromScss(scssContent);
  const cssColors = extractColorsFromCss(cssContent);
  
  console.log(`📊 Found ${scssColors.size} colors in m3-theme.scss`);
  console.log(`📊 Found ${cssColors.size} colors in _color.scss\n`);
  
  // Verify critical color mappings
  let allSynced = true;
  
  console.log('🎯 CRITICAL COLOR VERIFICATION:\n');
  
  Object.entries(COLOR_MAPPINGS).forEach(([color, [name, scssLocation, cssLocation]]) => {
    const colorLower = color.toLowerCase();
    const inScss = scssColors.has(colorLower);
    const inCss = cssColors.has(colorLower);
    
    if (inScss && inCss) {
      console.log(`✅ ${name}: ${color} - SYNCED`);
    } else {
      console.log(`❌ ${name}: ${color} - MISSING`);
      if (!inScss) console.log(`   Missing in: ${scssLocation}`);
      if (!inCss) console.log(`   Missing in: ${cssLocation}`);
      allSynced = false;
    }
  });
  
  console.log('\n' + '='.repeat(50));
  
  if (allSynced) {
    console.log('✅ ALL CRITICAL COLORS ARE SYNCHRONIZED!');
    console.log('🎉 Figma colors are properly aligned between both files.');
  } else {
    console.log('❌ COLOR SYNC ISSUES DETECTED!');
    console.log('🚨 Please update both files to maintain consistency.');
    process.exit(1);
  }
}

// Run verification
verifyColorSync();
