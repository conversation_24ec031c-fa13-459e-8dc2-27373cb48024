import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  effect,
  inject,
  NgZone,
  OnInit,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import {
  MatDialogConfig,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { NgxFileDropEntry, NgxFileDropModule } from 'ngx-file-drop';
import { DataViewUploadTableHeading } from '../../shared/models';
import { UploadFilesTableComponent } from './upload-files-table/upload-files-table.component';
import { UploadPlaceholderComponent } from './upload-placeholder/upload-placeholder.component';
import { DataViewComponentStore } from '../../component-store/data-view.store';
import { ToastrService } from 'ngx-toastr';
import {
  checkDuplicateInHierarchy,
  convertFileEntryToFile,
  fileToBase64,
  getFileBlob,
  readFileAsArrayBuffer,
  validFileType,
} from '../../shared/files.methods';
import J<PERSON>Zip from 'jszip';
import {
  DataViewFile,
  DirectoryEntry,
  UploadedFile,
} from '../../shared/data-view.interfaces';
import { SharedModule } from '../../../../shared/shared.module';

@Component({
  selector: 'app-data-view-file-upload',
  imports: [
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    UploadFilesTableComponent,
    NgxFileDropModule,
    UploadPlaceholderComponent,
    SharedModule,
  ],
  templateUrl: './data-view-file-upload.component.html',
  styleUrl: './data-view-file-upload.component.css',
  providers: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataViewFileUploadComponent implements OnInit {
  // Reactive state - automatically updates when store changes
  uploadedFilesList = computed(() => this.store.uploadedFilesList());
  hierarchy = computed(() => this.store.hierarchy());
  selectedFolderPath = computed(() => this.store.selectedFolderPath());
  private rootDirectory: DirectoryEntry = {
    type: 'directory',
    name: 'root',
    children: [],
  };
  tableHeading = DataViewUploadTableHeading;
  private store = inject(DataViewComponentStore);
  readonly dialogRef = inject(MatDialogRef<DataViewFileUploadComponent>);

  static viewConfig: MatDialogConfig = {
    maxWidth: '100%',
    width: '860px',
    disableClose: true,
    maxHeight: '100%',
    height: '560px',
  };
  folderPath = '';
  isLoading = false;
  constructor(
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone,
    private toastrService: ToastrService,
  ) {}
  ngOnInit(): void {
    this.folderPath = this.store.selectedFolderPath();
  }

  OnEffect = effect(() => {
    const isFileUploaded = this.store.isUploadDone();
    if (!isFileUploaded && this.isLoading) {
      this.isLoading = false;
      this.dialogRef.close();
    }
    this.cdr.markForCheck();
  });
  onFileInputChange() {
    this.cdr.markForCheck();
  }

  dropped(files: NgxFileDropEntry[]) {
    this.resetState();

    const directoryFiles = this.filterFilesByDirectory(files);
    const zipFile = this.filterFilesByZip(files);

    if (directoryFiles.length > 0) {
      this.processIndividualFiles(directoryFiles);
    } else if (zipFile.length > 0) {
      this.processZipFile(zipFile[0]);
    } else {
      this.processIndividualFiles(files);
    }
  }

  filterFilesByDirectory(files: NgxFileDropEntry[]): NgxFileDropEntry[] {
    return files.filter(
      file =>
        file.relativePath &&
        file.relativePath.includes('/') &&
        file.fileEntry.isFile,
    );
  }

  private filterFilesByZip(files: NgxFileDropEntry[]): NgxFileDropEntry[] {
    return files.filter(file => /\.zip$/i.test(file.relativePath));
  }

  private async processZipFile(file: NgxFileDropEntry): Promise<void> {
    const fileEntry = file.fileEntry as FileSystemFileEntry;

    fileEntry.file(async (zipFile: File) => {
      try {
        const arrayBuffer = await readFileAsArrayBuffer(zipFile);
        const zip = await JSZip.loadAsync(arrayBuffer);

        const dataViewFiles: DataViewFile[] = [];

        // Loop through all entries in the ZIP
        const zipEntries = Object.values(zip.files); // Ensure sequential iteration

        for (const zipEntry of zipEntries) {
          if (!zipEntry.dir) {
            const blob = await zipEntry.async('blob');
            const unzippedFile = new File(
              [blob],
              zipEntry.name.split('/').pop() ?? '',
              {
                type: blob.type,
              },
            );

            const dataViewFile: DataViewFile = {
              file: unzippedFile,
              fullPath: zipEntry.name.includes('/')
                ? zipEntry.name.substring(0, zipEntry.name.lastIndexOf('/'))
                : zipEntry.name,
            };

            dataViewFiles.push(dataViewFile);

            console.log(zipEntry, 'zip entry');
            // Optionally update visual tree
            const fileEntry = {
              type: 'file' as const,
              name: zipEntry.name.split('/').pop() || zipEntry.name,
              fullPath: dataViewFile.fullPath,
              path: await fileToBase64(unzippedFile),
            };

            this.rootDirectory.children.push(fileEntry);
          }
        }

        // Now pass all extracted files to your existing addFilesToList function
        this.addFilesToList(dataViewFiles);
        this.cdr.markForCheck();
      } catch (error) {
        console.error('❌ Failed to process zip file:', error);
      }
    });
  }

  processIndividualFiles(files: NgxFileDropEntry[]): void {
    const fileEntries = files.map(
      file => file.fileEntry as FileSystemFileEntry,
    );

    Promise.all(fileEntries.map(fileEntry => convertFileEntryToFile(fileEntry)))
      .then(convertedFiles => {
        this.addFilesToList(convertedFiles);
        this.ngZone.run(() => {
          this.cdr.markForCheck();
        });
      })
      .catch(error => {
        console.error('Error processing dropped files:', error);
        // this.toastrService.error('Error processing dropped files');
      });
  }

  closeModal() {
    this.dialogRef.close();
    this.store.setUploadedFiles([]);
  }

  uploadDirectoryStructureOrZipFile(file: File) {
    console.log(file);
  }

  private resetState() {
    this.rootDirectory.children = [];
  }

  openFileSelector() {
    console.log('open file selector');
  }

  onFileRemoved(index: number) {
    // Remove from component store - signals will handle the rest automatically
    this.store.removeUploadedFile(index);

    // Remove from root directory children if it exists
    if (this.rootDirectory.children.length > index) {
      this.rootDirectory.children = [
        ...this.rootDirectory.children.slice(0, index),
        ...this.rootDirectory.children.slice(index + 1),
      ];
    }
  }

  addFilesToList(files: DataViewFile | DataViewFile[] | DataViewFile) {
    console.log(files, 'files');
    const uploadedFilesList: UploadedFile[] = this.store.uploadedFilesList();
    // Convert the FileList or single file to an array if needed
    const fileArray: DataViewFile[] =
      files instanceof FileList
        ? Array.from(files).map(file => ({
            file,
            fullPath:
              file.webkitRelativePath.substring(
                0,
                file.webkitRelativePath.lastIndexOf('/'),
              ) ?? file.name, // fallback if no path
          }))
        : Array.isArray(files)
          ? (files as DataViewFile[])
          : [files as DataViewFile];

    // Handle each file
    fileArray.forEach(file => {
      // Skip if file is already in the list (avoid duplicates)
      const fileAlreadyExists = uploadedFilesList.some(
        item =>
          item.file.file.name === file.file.name &&
          item.file.file.size === file.file.size,
      );

      // Skip if file is already in the tree (avoid duplicates)
      // check the path we are on - selectedFolderPath
      // check the file name and size
      const fileAlreadyExistsInTree = checkDuplicateInHierarchy(
        file.file,
        this.selectedFolderPath(),
        this.hierarchy(),
        'file',
      );

      const isValidType = validFileType(file.file);
      if (!isValidType) {
        this.toastrService.error(`${file.file.name} is not allowed`);
        return;
      }
      if (fileAlreadyExists || fileAlreadyExistsInTree) {
        // show toast
        this.toastrService.error(`${file.file.name} already exists`);
        return;
      }

      // Add the file to the list with initial progress of 0
      uploadedFilesList.push({
        file,
        progress: 0,
        uploading: false,
      });
      this.store.setUploadedFiles(uploadedFilesList);
    });
  }

  uploadFiles() {
    this.isLoading = true;
    const files = this.uploadedFilesList();
    if (files.length === 0) {
      return;
    }

    this.processRegularFiles(files);
  }

  async processRegularFiles(regularFiles: UploadedFile[]): Promise<void> {
    if (regularFiles.length === 0) return;
    const fileBlob: Blob[] = [];
    try {
      // Convert all regular files to base64 and update state
      for (const fileItem of regularFiles) {
        await this.processSingleRegularFile(fileItem);
      }

      for (const fileEntry of this.rootDirectory.children) {
        if (fileEntry.type === 'file') {
          const blob = (await getFileBlob(fileEntry)) as Blob;
          fileBlob.push(blob);
          if (!fileBlob) {
            console.error('Failed to get file blob for', fileEntry.name);
            continue;
          }
        } else {
          fileBlob.push(new Blob([], { type: 'application/octet-stream' }));
        }
      }

      if (this.rootDirectory.children.length > 0) {
        regularFiles.forEach(item => (item.progress = 80));

        try {
          this.store.getPresignedUrl({
            fileBlobs: fileBlob,
            folderPath: this.store.selectedFolderPath(),
          });
          regularFiles.forEach(item => (item.progress = 100));
        } catch (uploadError) {
          console.error('DEBUG: Error uploading regular files:', uploadError);
        }
      }
    } catch (batchError) {
      console.error(
        'DEBUG: Error in batch processing regular files:',
        batchError,
      );
    }
  }

  private async processSingleRegularFile(
    fileItem: UploadedFile,
  ): Promise<void> {
    const file = fileItem.file.file;
    fileItem.uploading = true;
    fileItem.progress = 10;

    try {
      const base64 = await fileToBase64(file);
      fileItem.progress = 50;

      const fileEntry = {
        type: 'file' as const,
        name: file.name,
        path: base64,
      };

      this.rootDirectory.children.push(fileEntry); // ✅ direct mutation

      fileItem.progress = 70;
    } catch (error) {
      console.error(`DEBUG: Error processing file ${file.name}:`, error);
      fileItem.progress = 0;
      throw error;
    }
  }
}
