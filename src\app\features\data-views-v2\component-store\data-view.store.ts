import { inject } from '@angular/core';
import {
  catchError,
  EMPTY,
  firstValue<PERSON>rom,
  forkJoin,
  from,
  interval,
  last,
  of,
  pipe,
  switchMap,
  takeWhile,
  tap,
} from 'rxjs';
import {
  patchState,
  signalStore,
  withHooks,
  withMethods,
  withState,
} from '@ngrx/signals';
import { withDevtools } from '@angular-architects/ngrx-toolkit';
import { DataViewServiceV2 } from '../services/data-view.service';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { getFileBlob, updateHierarchy } from '../shared/files.methods';
import {
  DataViewResponse,
  FileDirectoryEntry,
  PresignedUrlResponse,
  UploadedFile,
} from '../shared/data-view.interfaces';
import { ToastrService } from 'ngx-toastr';

export const DataViewInitialState: DataViewComponentState = {
  hierarchy: {},
  isLoading: true,
  selectedFolderPath: '',
  uploadedFilesList: [],
  project_id: 0,
  sort_value: {
    type: '',
    value: '',
  },
  search: '',
  loadedPaths: [],
  isUploadDone: false,
  newAddedFileIds: [],
  availableFiles: new Set<number>(),
  previewFileMetadata: {
    fileId: 0,
    showPreview: false,
  },
  projectTitle: '',
};

export interface DataViewComponentState {
  hierarchy: Record<number, FileDirectoryEntry[]>;
  isLoading: boolean;
  uploadedFilesList: UploadedFile[];
  selectedFolderPath: string;
  project_id: number;
  loadedPaths: string[];
  isUploadDone: boolean;
  sort_value: {
    type: string;
    value: string;
  };
  availableFiles: Set<number>;
  newAddedFileIds: number[];
  search: string;
  previewFileMetadata: {
    fileId: number;
    showPreview: boolean;
  };
  projectTitle: string;
}

export const DataViewComponentStore = signalStore(
  { providedIn: 'root' },
  withState<DataViewComponentState>(DataViewInitialState),
  withDevtools('DataViewStore'),

  withMethods(
    (
      store,
      dataViewService = inject(DataViewServiceV2),
      _toasterService = inject(ToastrService),
    ) => ({
      async loadFiles(
        projectId: number,
        isLoading = false,
        path = '',
        page = 1,
      ) {
        this.setLoading(isLoading);
        try {
          const files = await firstValueFrom(
            dataViewService.getFilesList(
              projectId,
              page,
              10,
              path.startsWith('/') ? path.slice(1) : path,
              store.sort_value(),
              store.search(),
            ),
          );
          const hierarchy: FileDirectoryEntry[] = [];
          files.data.files.forEach(
            (file: { name: string; id: number; folder: string }) => {
              this.setAvailableFiles(file.id);
              hierarchy.push({
                name: file.name,
                path: `/${file.folder}`,
                type: 'file',
                id: file.id,
                parentPath: path,
              });
            },
          );

          if (files.data.pagination.has_next) {
            hierarchy.push({
              name: `Show All (${files.data.pagination.total_items - 10})`,
              path: `/${files.data.folder}`,
              type: 'loadMore',
              id: -1,
              parentPath: path,
              totalItems: files.data.pagination.total_items,
            });
          }
          const folder = files.data.folder;
          files.data.subfolders.forEach((folderName: string) => {
            hierarchy.push({
              name: folderName,
              path: folder ? `/${folder}/${folderName}` : `/${folderName}`,
              type: 'folder',
              parentPath: path,
              id: 0,
              children: [],
            });
          });
          this.setLoadedPaths(path.startsWith('/') ? path.slice(1) : path);
          const existingFiles = store.hierarchy()[projectId];
          if (existingFiles) {
            const updatedHierarchy = updateHierarchy(
              existingFiles,
              path,
              hierarchy,
            );
            this.setFiles(updatedHierarchy, projectId);
          } else {
            this.setFiles(hierarchy, projectId);
          }
          this.setLoading(false);
        } catch (error) {
          console.error('Failed to load files:', error);
        }
      },
      getFileBlobsd() {
        return from(
          Promise.all(
            store.uploadedFilesList().map(file => getFileBlob(file.file.file)),
          ),
        ).pipe(
          tap((fileBlobs: (Blob | null)[]) => {
            console.log(fileBlobs);
          }),
        );
      },

      getPresignedUrl: rxMethod<{ fileBlobs: Blob[]; folderPath: string }>(
        pipe(
          tap(() => patchState(store, { isLoading: true })),
          switchMap(({ fileBlobs }) => {
            const payload = store.uploadedFilesList().map(file => ({
              file_name: file.file.file.name,
              size: file.file.file.size,
            }));

            return dataViewService.presignedUrl(payload).pipe(
              switchMap((response: DataViewResponse<PresignedUrlResponse>) => {
                const fileKeys = response.data.map(item => item.file_key);
                const signedUrl = response.data.map(item => item.signed_url);
                const updatedUploadedFiles = store
                  .uploadedFilesList()
                  .map((file, index) => ({
                    ...file,
                    file_key: fileKeys[index],
                  }));
                const blobRequests = fileBlobs.map((blob: Blob, index) => {
                  const url = signedUrl[index];
                  return dataViewService.signedUrlUpload(url, blob).pipe(
                    catchError(error => {
                      console.error(`Upload failed for file :`, error);
                      patchState(store, { isUploadDone: true });
                      return of(null);
                    }),
                  );
                });

                //Wait for all to complete and collect results
                // return forkJoin(blobRequests)
                return forkJoin(blobRequests).pipe(
                  // After all uploads succeed, call process API for each file_key
                  switchMap(() => {
                    const processObservables = updatedUploadedFiles.map(item =>
                      dataViewService
                        .processFile({
                          file_name: item.file.file.name,
                          file_key: item.file_key,
                          folder:
                            `${store.selectedFolderPath()}/${item.file.fullPath}`.replace(
                              /^\/|\/$/g,
                              '',
                            ),
                          project_id: store.project_id(),
                          size: item.file.file.size,
                        })
                        .pipe(
                          // Poll every 2 seconds until status becomes 'success'
                          switchMap(response => {
                            const taskId = response.data.task_id;
                            return interval(2000).pipe(
                              switchMap(() =>
                                dataViewService.getFileProcessStatus(taskId),
                              ),
                              takeWhile(
                                statusResponse =>
                                  statusResponse.data.status !== 'SUCCESS',
                                true,
                              ),
                              last(),
                              tap(() => {
                                _toasterService.success(
                                  'File uploaded successfully!',
                                );
                              }),
                              catchError(error => {
                                console.error(
                                  `Error polling status for ${item.file_key}:`,
                                  error,
                                );
                                return of(null); // Let other observables continue
                              }),
                            );
                          }),
                          catchError(error => {
                            console.error(
                              `Error processing file ${item.file_key}:`,
                              error,
                            );
                            return of(null);
                          }),
                        ),
                    );

                    // Wait for all process + polling status calls to finish
                    return forkJoin(processObservables);
                  }),
                );
              }),
              tap(finalUrls => {
                console.log('Final Signed URLs', finalUrls);
                patchState(store, { isLoading: false, isUploadDone: true });
              }),
              catchError(err => {
                console.error('Failed in presigned URL flow', err);
                patchState(store, { isLoading: false, isUploadDone: true });
                return EMPTY;
              }),
            );
          }),
        ),
      ),

      deleteFile(id: number, project_id: number) {
        return dataViewService.DeleteFiles(id).subscribe({
          next: response => {
            patchState(store, state => ({
              ...state,
              hierarchy: {},
              isLoading: false,
              loadedPaths: [],
              previewFileMetadata: {
                ...state.previewFileMetadata,
                showPreview: false,
              },
            }));
            _toasterService.success(response.message);
            this.loadFiles(project_id, true);
          },
        });
      },

      deleteFolder(path: string, project_id: number) {
        return dataViewService.DeleteFolder(path, project_id).subscribe({
          next: response => {
            patchState(store, state => ({
              ...state,
              isLoading: false,
              hierarchy: {},
              loadedPaths: [],
              previewFileMetadata: {
                ...state.previewFileMetadata,
                showPreview: false,
              },
            }));
            _toasterService.success(response.message);
            this.loadFiles(project_id, true);
          },
        });
      },

      setProjectInfo() {
        return dataViewService
          .getProjectInfo(Number(localStorage.getItem('project_id')))
          .subscribe(response => {
            patchState(store, state => ({
              ...state,
              projectTitle: response.data.title,
            }));
          });
      },
      async loadMoreFiles(
        projectId: number,
        isLoading = false,
        path = '',
        page: number,
        size: number,
      ) {
        this.setLoading(isLoading);
        console.log(size, 'size');
        try {
          const files = await firstValueFrom(
            dataViewService.getFilesList(
              projectId,
              page,
              10,
              path.startsWith('/') ? path.slice(1) : path,
              store.sort_value(),
              store.search(),
              true,
            ),
          );
          const hierarchy: FileDirectoryEntry[] = [];
          files.data.files.forEach(
            (file: { name: string; id: number; folder: string }) => {
              this.setNewlyAddedFiles(file.id);
              hierarchy.push({
                name: file.name,
                path: `/${file.folder}`,
                type: 'file',
                id: file.id,
                parentPath: path,
              });
            },
          );

          //remove showall from existing
          const existingFiles = store.hierarchy()[projectId];
          const updatedHierarchy = updateHierarchy(
            existingFiles,
            path,
            hierarchy,
            true,
          );
          this.setFiles(updatedHierarchy, projectId);
          this.setLoading(false);
        } catch (error) {
          console.error('Failed to load files:', error);
        }
      },

      setFiles(files: FileDirectoryEntry[], projectId: number): void {
        patchState(store, state => ({
          ...state,
          hierarchy: {
            ...state.hierarchy,
            [projectId]: files,
          },
        }));
      },

      setLoading(isLoading: boolean): void {
        patchState(store, state => ({
          ...state,
          isLoading: isLoading,
        }));
      },

      setSortValue(sortValue: string, type: string) {
        patchState(store, state => ({
          ...state,
          sort_value: {
            type: type,
            value: sortValue,
          },
        }));
      },

      setSearchTerm(searchTxt: string) {
        patchState(store, state => ({
          ...state,
          search: searchTxt,
        }));
      },

      setUploadedFiles(files: UploadedFile[]): void {
        patchState(store, state => ({
          ...state,
          uploadedFilesList: files,
        }));
      },

      removeUploadedFile(index: number): void {
        patchState(store, state => ({
          ...state,
          uploadedFilesList: [
            ...state.uploadedFilesList.slice(0, index),
            ...state.uploadedFilesList.slice(index + 1),
          ],
        }));
      },

      setSelectedFolderPath(path: string) {
        patchState(store, state => ({
          ...state,
          selectedFolderPath: path.startsWith('/') ? path.slice(1) : path,
        }));
      },

      setNewlyAddedFiles(id: number, reset = false) {
        patchState(store, state => ({
          ...state,
          newAddedFileIds: reset ? [] : [...state.newAddedFileIds, id],
        }));
      },

      setAvailableFiles(id: number) {
        patchState(store, state => ({
          ...state,
          availableFiles: state.availableFiles.add(id),
        }));
      },

      setProjectDetails(project_id: number) {
        patchState(store, state => ({
          ...state,
          project_id: project_id,
        }));
      },

      setPreviewFileId(id: number, showPreview: boolean) {
        patchState(store, state => ({
          ...state,
          previewFileMetadata: {
            fileId: id,
            showPreview: showPreview,
          },
        }));
      },

      setLoadedPaths(newPath: string) {
        patchState(store, state => ({
          ...state,
          loadedPaths: [...state.loadedPaths, newPath],
        }));
      },

      setIsUploadDone(isUploaded: boolean) {
        patchState(store, state => ({
          ...state,
          isUploadDone: isUploaded,
        }));
      },

      resetState() {
        patchState(store, () => ({
          hierarchy: {},
          isLoading: true,
          selectedFolderPath: '',
          loadedPaths: [],
          uploadedFilesList: [],
          search: '',
          sort_value: {
            type: '',
            value: '',
          },
          previewFileMetadata: {
            fileId: 0,
            showPreview: false,
          },
          availableFiles: new Set<number>(),
          isUploadDone: false,
        }));
      },
      resetPartialState() {
        patchState(store, () => ({
          hierarchy: {},
          isLoading: true,
          selectedFolderPath: '',
          loadedPaths: [],
          uploadedFilesList: [],
          previewFileMetadata: {
            fileId: 0,
            showPreview: false,
          },
          availableFiles: new Set<number>(),
        }));
      },
    }),
  ),

  withHooks({
    onInit(store) {
      store.setProjectDetails(Number(localStorage.getItem('project_id')));
    },
    onDestroy(store) {
      store.resetState();
      store.resetPartialState();
    },
  }),
);
