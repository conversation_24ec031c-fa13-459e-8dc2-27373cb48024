import { ProjectData } from '../../dashborad/models/project.model';
import { PlotDataUI } from '../models/plot.model';

export interface VisualDataState {
  loadedPlotsData: {
    plots: PlotDataUI[];
    meta_data: {
      total: number;
      max_page: number;
    };
  } | null;
  isLoading: boolean;
  filter: object;
  error: Error | null;
  projectData: ProjectData | null;
  pagination: {
    totalItems: number;
    currentCursor: number;
    limit: number;
  };
  showFavouritePlots: boolean;
}
