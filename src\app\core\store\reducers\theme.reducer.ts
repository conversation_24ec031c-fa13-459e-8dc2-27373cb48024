import { createReducer, on } from '@ngrx/store';
import { ThemeState } from '../states/theme.state';
import { toggleTheme, setTheme } from '../actions/theme.action';

const initialState: ThemeState = {
  themeMode: 'light',
};

export const themeReducer = createReducer(
  initialState,
  on(toggleTheme, (state): ThemeState => {
    return {
      ...state,
      themeMode: state.themeMode === 'light' ? 'dark' : 'light',
    };
  }),
  on(setTheme, (state, { themeMode }) => {
    return {
      ...state,
      themeMode,
    };
  }),
);
