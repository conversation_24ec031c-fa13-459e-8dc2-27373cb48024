.file-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 5px 8px;
}

.file-item {
  position: relative;
}

.file-item:hover {
  background-color: rgba(103, 80, 164, 0.12);
  border-radius: 0.375rem;
}

.hidden-on-hover {
  display: none;
}

.file-item:hover .hidden-on-hover {
  display: inline-block;
}

.table-file {
  display: inline-block;
}

.folder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.delete-folder {
  margin-left: auto;
  /* Aligns delete icon to the right */
  margin-right: 8px;
  /* Adds a small gap at the end */
  visibility: hidden;
  /* Hidden by default */
}

.folder-header:hover .delete-folder {
  visibility: visible;
  /* Shows the delete icon when hovering */
}

.view-file-btn,
.delete-icon {
  display: none;
  cursor: pointer;
}

.file-item:hover .view-file-btn,
.file-item:hover .delete-icon {
  display: inline-block;
}

.view-file-btn {
  background-color: white;
  border-radius: 100px;
  color: #296197;
  padding: 5px 10px;
}

.image-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.table-file {
  display: flex;
  flex-direction: row;
  align-items: center;
}

mat-expansion-panel {
  border: none !important;
  box-shadow: none !important;
}

mat-expansion-panel-header {
  border: none !important;
  box-shadow: none !important;
}

mat-expansion-panel-header {
  background-color: transparent !important;
}

ng-container {
  border: none;
}

.colom-btn {
  border: 1;
  padding: 6px 16px;
  background-color: white;
  border-radius: 100px;
  margin-right: 10px;
  margin-bottom: 4px;
}

.mat-elevation-z8 {
  box-shadow: none;
  background-color: white;
}

.table-modal {
  max-width: 420px;
  background-color: #f7f9ff;
  padding: 20px;
}

.mat-table {
  border-spacing: 0;
}

.mat-cell {
  padding: 4px 8px;
}

.image-modal {
  width: 420px;
  background-color: #f7f9ff;
  padding: 20px;
}

.example-container {
  height: 420px;
  width: 550px;
  max-width: 100%;
  overflow: auto;
}

td.mat-column-star {
  width: 20px;
  padding-right: 8px;
}

th.mat-column-position,
td.mat-column-position {
  padding-left: 8px;
}

.mat-mdc-table-sticky-border-elem-right {
  border-left: 1px solid #e0e0e0;
}

.mat-mdc-table-sticky-border-elem-left {
  border-right: 1px solid #e0e0e0;
}

.mat-table {
  transition: none !important;
  animation: none !important;
}

.mat-row,
.mat-header-row {
  transition: none !important;
  animation: none !important;
}

.common-text {
  margin-top: 20px;
  font-size: normal;
  line-height: 20px;
}

.fixed {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.bg-opacity-50 {
  background-color: rgba(0, 0, 0, 0.5);
}

.border-line {
  border-bottom: 1px solid #6750a41f !important;
}

.folder-delete {
  float: right;
  text-align: right;
  position: absolute;
  right: 40px;
  font-size: 15px;
  top: 16px;
}

::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f1f3f9;
  /* Background color of the track */
  border-radius: 10px;
  /* Rounding the track */
}

/* Handle */
::-webkit-scrollbar-thumb {
  background-color: #b8c4ce;
  /* Scrollbar thumb color */
  border-radius: 10px;
  /* Rounding the thumb */
  border: 3px solid #f1f3f9;
  /* Space between track and thumb */
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background-color: #d3cccc;
  /* Darker color when hovered */
}

td,
th {
  text-align: center;
  border: 1px solid #ecebf8;
}

td:last-child,
th:last-child {
  border-right: none;
}

table tr:nth-child(odd) {
  background-color: #f1f3f9;
}

table th {
  background-color: #f7f9ff;
}

td {
  color: #82868e;
  font-weight: 400;
}

.highlight_row {
  background-color: #296197 !important;
  color: white;
}

::ng-deep
  mat-expansion-panel-header.highlight_row
  .mat-expansion-indicator
  svg {
  fill: white;
}
