{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"AICU": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/aicu", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "src/style.css", "node_modules/ngx-toastr/toastr.css", "node_modules/intro.js/introjs.css"], "scripts": ["node_modules/plotly.js-dist/plotly.js", "node_modules/intro.js/intro.js", "./node_modules/intro.js/intro.js"], "server": "src/main.server.ts", "prerender": true, "ssr": {"entry": "server.ts"}}, "configurations": {"production": {"index": "src/index.prod.html", "fileReplacements": [{"replace": "src/app/env/env.ts", "with": "src/app/env/env.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "900kB", "maximumError": "6MB"}, {"type": "anyComponentStyle", "maximumWarning": "6kB", "maximumError": "10kB"}], "outputHashing": "all"}, "development": {"index": "src/index.dev.html", "fileReplacements": [{"replace": "src/app/env/env.ts", "with": "src/app/env/env.dev.ts"}], "optimization": false, "extractLicenses": false, "sourceMap": true}, "test": {"index": "src/index.test.html", "fileReplacements": [{"replace": "src/app/env/env.ts", "with": "src/app/env/env.test.ts"}], "optimization": false, "extractLicenses": false, "sourceMap": true}, "local": {"index": "src/index.local.html", "fileReplacements": [{"replace": "src/app/env/env.ts", "with": "src/app/env/env.local.ts"}], "optimization": false, "extractLicenses": false, "sourceMap": true}, "beta": {"index": "src/index.beta.html", "fileReplacements": [{"replace": "src/app/env/env.ts", "with": "src/app/env/env.beta.ts"}], "optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "AICU:build:production"}, "development": {"buildTarget": "AICU:build:development"}, "local": {"buildTarget": "AICU:build:local"}, "test": {"buildTarget": "AICU:build:test"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}}