<div>
  <h3 class="preview-title">Preview</h3>
  <div class="preview-table">
    <table>
      <tbody>
        @for (item of previewItems; track item.label; let i = $index) {
          <tr [class]="i % 2 === 0 ? 'preview-row-white' : 'preview-row-gray'">
            <td
              [class]="
                i === previewItems.length - 1
                  ? 'preview-cell-label preview-cell-last'
                  : 'preview-cell-label'
              ">
              {{ item.label }}
            </td>
            <td
              [class]="
                i === previewItems.length - 1
                  ? 'preview-cell-value preview-cell-last'
                  : 'preview-cell-value'
              ">
              {{ item.value }}
            </td>
          </tr>
        }
      </tbody>
    </table>
  </div>
</div>
