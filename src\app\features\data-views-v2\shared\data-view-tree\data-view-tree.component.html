@if (!isLoading && !isEmpty) {
  <div>
    <mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
      <mat-tree-node
        *matTreeNodeDef="let node"
        matTreeNodePadding
        (click)="showPreview(node.item.id)">
        <div class="file-item">
          <mat-icon class="mr-2">table_chart</mat-icon>

          <div>{{ node.item.name }}</div>
          <div class="hidden-on-hover">
            <button class="view-file-btn" (click)="goToViewFile(node.item.id)">
              View File
            </button>
            <button
              mat-icon-button
              class="icon-button"
              (click)="deleteFile(node.item.id)">
              <mat-icon>delete_outline</mat-icon>
            </button>
          </div>
        </div>
      </mat-tree-node>

      <mat-tree-node
        *matTreeNodeDef="let node; when: hasChild"
        matTreeNodePadding
        matTreeNodeToggle
        [cdkTreeNodeTypeaheadLabel]="node.item"
        [ngClass]="{ 'highlight-node': node.item.path === currentPath }">
        <button
          matIconButton
          [attr.aria-label]="'Toggle ' + node.item"
          matTreeNodeToggle>
          <mat-icon class="ml-2">folder_open</mat-icon>
          <span class="folder-name">{{ node.name }} </span>
        </button>
        {{ node.item.name }}
        <div class="folder-controls">
          @if (allowFolderDelete(node)) {
            <span>
              <button
                mat-icon-button
                class="icon-button view-file-btn"
                (click)="deleteFolder(node.item.path)">
                <mat-icon>delete_outline</mat-icon>
              </button>
            </span>
          }
          <mat-icon>{{
            treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right'
          }}</mat-icon>
        </div>
      </mat-tree-node>
      <mat-tree-node
        class="example-load-more"
        *matTreeNodeDef="let node; when: isLoadMore"
        role="treeitem"
        (click)="loadNextPage(node)"
        [style.padding-left.px]="node.level * 40">
        <p style="padding-left: 8px">{{ node.item.name }}</p>
      </mat-tree-node>
    </mat-tree>
  </div>
} @else {
  <app-empty-state-card
    [title]="searchFilter ? 'File Not Found' : 'Add Your First File'"
    [description]="
      searchFilter
        ? 'Try a different keyword or clear the Filters.'
        : 'To begin innovating, add your first data file. Click on Add Data and drag folders or files to the screen.'
    "></app-empty-state-card>
}
