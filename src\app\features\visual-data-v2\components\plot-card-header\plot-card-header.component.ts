import { Component, computed, inject, Input } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltip } from '@angular/material/tooltip';
import { visualDataStore } from '../../store/visual-data.store';
import { MatDialog } from '@angular/material/dialog';
import { PlotDataFilterModalComponent } from '../plot-data-filter-modal/plot-data-filter-modal.component';

@Component({
  selector: 'app-plot-card-header',
  imports: [MatMenuModule, MatIconModule, MatButtonModule, MatTooltip],
  templateUrl: './plot-card-header.component.html',
  styleUrl: './plot-card-header.component.css',
})
export class PlotCardHeaderComponent {
  @Input({ required: true }) plotId!: number;
  visualDataStore = inject(visualDataStore);
  isFullScreen = false;
  constructor(private dialog: MatDialog) {}

  //TO-DO
  /*
  AMAN : 
  I dont like this approach of using computed properties to make it reactive for Mark as favourite 
  unmark as favourite use-case based on changes in favorite value in plotData inside the array of plots data which we receive from the API.
  (this problem is known as classic partially change detection problem in angular)
  but anyways I have implemented for the time being.
  
  I DONT WANT : 
  - I dont want to use CDR for change detection

  Possible POC:
  - Pass the input from parent as signal visualStore.loadedPlotsData().find( plot => plot.id === this.plotId)
  - Capture the value in this component as normal number
  - Check if the reactive works or not
  */
  plotData = computed(() => {
    return this.visualDataStore
      .loadedPlotsData()
      ?.plots.find(plot => plot.id === this.plotId);
  });

  openPlotDataFilterModal() {
    this.dialog.open(PlotDataFilterModalComponent, {
      minWidth: '70vw',
      minHeight: '30vh',
      enterAnimationDuration: '100ms',
      exitAnimationDuration: '100ms',
      data: {
        fileId: this.plotData()?.fileId,
        plotId: this.plotData()?.id,
        filterInstanceId: this.plotData()?.filter_instance_id,
        isFilterActive: this.plotData()?.filter_active,
      },
    });
  }

  toggleFullScreenMode() {
    if (this.isFullScreen) {
      document.exitFullscreen().then(() => {
        this.isFullScreen = false;
        window.dispatchEvent(new Event('resize')); // For Plotly Resizing
      });
    } else {
      const selector = `#plot-graph-${this.plotData()?.id}`;
      const elem = document.querySelector(selector);
      if (elem?.requestFullscreen) {
        elem.requestFullscreen().then(() => {
          this.isFullScreen = true;
          window.dispatchEvent(new Event('resize')); // For Plotly Resizing
        });
      }
    }
  }
}
