/* eslint-disable @typescript-eslint/no-explicit-any */

import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
} from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatStepperModule } from '@angular/material/stepper';
import { SelectFileFolderComponent } from './select-file-folder/select-file-folder.component';
import { TrainingService } from '../service/training-service.service';
import { ToastrService } from 'ngx-toastr';
import { MatCheckbox } from '@angular/material/checkbox';
import {
  ColumnInfo,
  ColumnInfoData,
  CreateMLmodelPayload,
  CreateMlmodelResponse,
  MachineLearningModelInfoInterface,
  MachineLearningModels,
  MLTrainingProjectInfo,
} from '../models/trainings.model';
import { Subscription } from 'rxjs';
import { SharedModule } from '../../../shared/shared.module';
import { TrainingPreviewComponent } from './training-preview/training-preview.component';
import { MatIcon } from '@angular/material/icon';

@Component({
  selector: 'app-training-dialog',
  imports: [
    SharedModule,
    CommonModule,
    MatButtonModule,
    MatDialogContent,
    MatStepperModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckbox,
    ReactiveFormsModule,
    SelectFileFolderComponent,
    TrainingPreviewComponent,
    MatIcon,
  ],
  templateUrl: './training-dialog.component.html',
  styleUrl: './training-dialog.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TrainingDialogComponent implements OnDestroy, OnInit {
  readonly dialogRef = inject(MatDialogRef<TrainingDialogComponent>);
  readonly data = inject<MLTrainingProjectInfo>(MAT_DIALOG_DATA);
  mlTasks: string[] = [];
  mlModelNames: MachineLearningModels[] = [];
  mlModelInfo!: MachineLearningModels[];
  machineLearningModels!: MachineLearningModels[];
  columnsInfo: ColumnInfo[] = [];
  targetColumnInfo: ColumnInfo[] = [];
  featureColumnInfo: ColumnInfo[] = [];
  subscriptions: Subscription = new Subscription();
  trainingForm!: FormGroup;
  previewData: any;
  selectedFile!: { id: number; name: string };
  preselectedFile!: { file_id: number; file_name: string };
  featuresInfo: ColumnInfo[] = [];
  targetsInfo: ColumnInfo[] = [];
  constructor(
    private formBuilder: FormBuilder,
    private trainingService: TrainingService,
    private toasterService: ToastrService,
    private cdr: ChangeDetectorRef,
  ) {}
  test_size = 10;
  ngOnInit(): void {
    this.getMlModels();
    this.trainingForm = this.formBuilder.group({
      name: ['', Validators.required],
      trainingData: [null, Validators.required],
      datasetVersion: ['Version 1'],
      analysisGoalName: ['', Validators.required],
      analysisGoalType: ['', Validators.required],
      selectedTarget: [null, Validators.required],
      selectedFeatures: [null, Validators.required],
      train_size: [
        100 - this.test_size,
        [Validators.min(5), Validators.max(95)],
      ],
      test_size: [this.test_size, [Validators.min(5), Validators.max(95)]],
    });
    this.trainingForm.controls['train_size'].valueChanges.subscribe(val => {
      this.trainingForm.patchValue(
        {
          train_size: val,
          test_size: 100 - val,
        },
        { emitEvent: false },
      );
    });
    this.trainingForm.controls['test_size'].valueChanges.subscribe(val => {
      this.trainingForm.patchValue(
        {
          train_size: 100 - val,
          test_size: val,
        },
        { emitEvent: false },
      );
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  // Populates the training form if it is an edit action
  populateTrainingForm() {
    if (this.data) {
      const selectedAnalysisGoal = this.machineLearningModels.find(
        (model: MachineLearningModels) =>
          model.id === Number(this.data?.machine_learning_model?.id),
      );
      this.setSelectedValue(selectedAnalysisGoal?.machine_learning_tasks);
      this.preselectedFile = {
        file_id: this.data.file?.id,
        file_name: this.data?.file?.file_name,
      };
      this.trainingForm.patchValue({
        name: this.data?.name,
        trainingData: this.data?.file?.id,
        analysisGoalName: selectedAnalysisGoal?.machine_learning_tasks,
        analysisGoalType: selectedAnalysisGoal?.id,
        selectedTarget: this.data?.selected_target.id,
        selectedFeatures: this.data?.selected_features.map(
          feature => feature.id,
        ),
        test_size: this.data?.test_size * 100,
        train_size: 100 - this.data?.test_size * 100,
      });
      this.getColumnInfo();
    }
    this.cdr.detectChanges();
  }

  // To create ML MOdel training
  createTraining(event: MouseEvent) {
    event.stopPropagation();
    const payload: CreateMLmodelPayload = {
      machine_learning_model: this.trainingForm.get('analysisGoalType')?.value,
      name: this.trainingForm.get('name')?.value,
      selected_target: this.trainingForm.get('selectedTarget')?.value,
      selected_features: this.trainingForm.get('selectedFeatures')?.value,
      test_size: Number(
        (this.trainingForm.get('test_size')?.value / 100).toFixed(2),
      ),
    };
    const file_id = this.trainingForm.get('trainingData')?.value;
    this.subscriptions.add(
      this.trainingService.createMlTraining(file_id, payload).subscribe({
        next: (res: CreateMlmodelResponse) => {
          this.toasterService.success(res.message);
          this.dialogRef.close();
        },
        error: errors => {
          const { error } = errors;
          this.toasterService.error(error?.errors?.error);
          this.dialogRef.close();
        },
      }),
    );
  }

  onNoClick(): void {
    this.dialogRef.close(true);
  }

  getSelectedFile(event: { id: number; name: string }) {
    this.selectedFile = event;
    this.trainingForm.get('trainingData')?.setValue(event.id);
    this.getColumnInfo();
  }

  // Retrieves the ML models displayed in the Analysis Goal dropdown
  getMlModels() {
    this.subscriptions.add(
      this.trainingService.getMlModels().subscribe({
        next: (res: MachineLearningModelInfoInterface) => {
          this.machineLearningModels = res.data;
          this.mlModelInfo = res.data;
          this.populateTrainingForm();
          const machine_learning_tasks = res.data.map(
            (task: MachineLearningModels) => task.machine_learning_tasks,
          );
          const uniqueTasksObj: Record<string, boolean> = {};
          this.mlTasks = machine_learning_tasks.filter((task: any) => {
            if (!uniqueTasksObj[task]) {
              uniqueTasksObj[task] = true;
              return true;
            }
            return false;
          });
          this.cdr.detectChanges();
        },
        error: error => {
          this.toasterService.error(error);
        },
      }),
    );
  }

  setSelectedValue(event: any) {
    this.mlModelNames = this.machineLearningModels
      ?.filter(
        (model: MachineLearningModels) =>
          model.machine_learning_tasks === (event.value || event),
      )
      .map((model: any) => ({
        name: model.name,
        id: model.id,
        machine_learning_tasks: model.machine_learning_tasks,
      }));
  }

  selectedTarget(event: MatSelectChange) {
    this.featureColumnInfo = this.featuresInfo.filter((item: ColumnInfo) => {
      return item.id !== event.value;
    });
  }

  selectedFeatures(event: MatSelectChange | { value: number[] }) {
    const { value } = event;
    this.featureColumnInfo.forEach(feature => {
      feature.checked = value.includes(feature.id);
    });
    this.targetColumnInfo = this.targetsInfo.filter((item: ColumnInfo) => {
      return !value.includes(item.id);
    });
  }
  getSelectedFeatures() {
    const selectedFeatures = this.trainingForm.get('selectedFeatures')?.value;
    return selectedFeatures;
  }

  // Retrieves the list of target and feature columns
  getColumnInfo() {
    const file_id = this.trainingForm.get('trainingData')?.value;
    this.subscriptions.add(
      this.trainingService.getTargetFeatures(file_id).subscribe({
        next: (res: ColumnInfoData) => {
          const {
            data: { features, targets },
          } = res;
          this.featuresInfo = features.map((column: ColumnInfo) => ({
            id: column.id,
            name: column.name,
            checked: false,
          }));
          this.targetsInfo = targets.map((column: ColumnInfo) => ({
            id: column.id,
            name: column.name,
            checked: false,
          }));
          this.featureColumnInfo = this.featuresInfo;
          this.targetColumnInfo = this.targetsInfo;
          const preSelectedFeatures = this.getSelectedFeatures();
          if (preSelectedFeatures && preSelectedFeatures.length > 0)
            this.selectedFeatures({ value: preSelectedFeatures });
        },
      }),
    );
  }

  getTrainingsDetails() {
    const selectedTargetName = this.targetsInfo.find(
      column => this.trainingForm.value.selectedTarget === column.id,
    )?.name;
    const selectedFeatureName =
      this.trainingForm.value.selectedFeatures &&
      this.trainingForm.value?.selectedFeatures.map((value: number) => {
        const item = this.featuresInfo.find(column => value === column.id);
        return item ? item?.name : null;
      });
    this.previewData = {
      ...this.trainingForm.value,
      fileName: this.selectedFile?.name,
      selectedFeatures: selectedFeatureName,
      selectedTarget: selectedTargetName,
    };
    return this.previewData;
  }

  getTransformedSelectedFeatureNames() {
    return this.trainingForm.value.selectedFeatures?.map((value: number) => {
      const item = this.columnsInfo.find(column => value === column.id);
      return item ? item.name : null;
    });
  }

  getTransformedSelectedTargetNames() {
    return this.trainingForm.value.selectedTarget?.map((value: number) => {
      const item = this.columnsInfo.find(column => value === column.id);
      return item ? item.name : null;
    });
  }

  checkStepper1FormError() {
    return (
      this.trainingForm.get('name')?.errors ||
      this.trainingForm.get('trainingData')?.errors ||
      this.trainingForm.get('analysisGoalName')?.errors ||
      this.trainingForm.get('analysisGoalType')?.errors
    );
  }
}
