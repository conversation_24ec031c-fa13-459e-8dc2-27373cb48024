import { Component, Input, OnInit } from '@angular/core';
import { AuthService } from '../../../features/auth/services/auth.service';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { IntroService } from '../../../introjs.service';
import { SettingsTabs } from './side-bar.model';
import { Store } from '@ngrx/store';
import { changeTab } from '../../../core/store/actions/tab.action';
import {
  selectSelectedTab,
  selectShowSidebar,
} from '../../../core/store/selectors/tab.selector';
import { TabState } from '../../../core/store/states/tab.state';

@Component({
  selector: 'app-side-bar',
  templateUrl: './side-bar.component.html',
  styleUrl: './side-bar.component.scss',
  // eslint-disable-next-line @angular-eslint/prefer-standalone
  standalone: false,
})
export class SideBarComponent implements OnInit {
  showSidebar = true;
  drawerMode: 'over' | 'side' = 'side';
  isDrawerOpened = true;
  isSmallScreen = false; // Tracks if it's a small screen
  crossScreen = false;
  settingsTabs = SettingsTabs;

  @Input() page = 'projects';
  isDrawerOpen = false;
  project_id: string | null = null;
  private routerEventsSubscription: Subscription | undefined;
  activeButton = 'overview';
  selectedTab!: string;
  // Array of values to check against the current URL
  routeValues = [
    'Overview',
    'data-view-v2',
    'data-version',
    'data-insights',
    'training',
    'results',
    'settings',
    'projects',
  ];
  constructor(
    private authService: AuthService,
    private router: Router,
    private tosterService: ToastrService,
    private location: Location,
    private breakpointObserver: BreakpointObserver,
    private introService: IntroService,
    private store: Store<TabState>,
  ) {}
  toggleDrawer() {
    this.isDrawerOpen = !this.isDrawerOpen;
    this.isSmallScreen = !this.isSmallScreen;
    this.crossScreen = true;
  }

  closetoggleDrawer() {
    this.isDrawerOpen = !this.isDrawerOpen;
    this.isSmallScreen = !this.isSmallScreen;
    this.crossScreen = false;
  }

  ngOnInit(): void {
    const storedButton = localStorage.getItem('activeButton');
    // this.introService.startTour('side-bar');
    if (storedButton) {
      this.activeButton = storedButton;
    }
    this.router.events.subscribe(() => {
      this.checkRoute();
    });
    this.checkRoute();

    this.breakpointObserver.observe([Breakpoints.Handset]).subscribe(result => {
      if (result.matches) {
        this.drawerMode = 'over';
        this.isDrawerOpened = true;
        this.isSmallScreen = true;
      } else {
        this.drawerMode = 'side';
        this.isDrawerOpened = true;
        this.isSmallScreen = false;
      }
    });

    this.store.select(selectSelectedTab).subscribe(tabSelected => {
      if (!tabSelected) {
        this.selectedTab = tabSelected;
        const currentTab = this.routeValues.find(value =>
          this.router.url.includes(value),
        );
        this.store.dispatch(
          changeTab({ selectedTab: currentTab || 'projects' }),
        );
      }
    });
    this.store.select(selectShowSidebar).subscribe(value => {
      this.showSidebar = value;
    });
  }
  goBack(): void {
    this.location.back();
  }

  checkRoute(): void {
    const currentUrl = this.router.url;

    // Check if the current URL contains any of the values from routeValues
    const matchingValue = this.routeValues.find(value =>
      currentUrl.includes(value),
    );

    if (matchingValue) {
      this.activeButton = matchingValue; // Set activeButton to the matching value
    }
  }

  logout(): void {
    this.authService.logout().subscribe({
      next: () => this.redirectToLogin(),
      error: () => this.redirectToLogin(),
    });
  }

  deleteUser(): void {
    this.authService.deleteUser().subscribe({
      next: () => {
        this.redirectToLogin();
        localStorage.clear();
      },
      error: () => this.redirectToLogin(),
    });
  }

  redirectToLogin(): void {
    localStorage.clear();
    this.router.navigate(['auth/login']);
  }
  extractProjectId(): void {
    const urlSegments = this.router.url.split('/');
    const idIndex = urlSegments.indexOf('project') + 1;

    if (idIndex > 0 && urlSegments[idIndex]) {
      this.project_id = urlSegments[idIndex];
    }
  }
  handleButtonClick(tabName: string): void {
    this.activeButton = tabName;
    this.store.dispatch(changeTab({ selectedTab: this.activeButton }));
    if (tabName === 'settings') {
      this.router.navigate(['/settings']);
      return;
    }
    if (tabName === 'projects') {
      this.router.navigateByUrl('/dashboard/projects');
      return;
    }

    this.project_id = localStorage.getItem('project_id');
    if (this.project_id) {
      this.router.navigate(['/project', this.project_id, tabName]);
    }
  }
}
